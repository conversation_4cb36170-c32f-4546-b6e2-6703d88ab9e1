/**
 * @file PinConfig.h
 * @brief 硬件引脚配置定义
 * 
 * ESP32-S3引脚分配和硬件配置
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef PIN_CONFIG_H
#define PIN_CONFIG_H

#include <Arduino.h>

// 红外相关引脚
#define IR_TRANSMIT_PIN         18
#define IR_RECEIVE_PIN          19

// LED和按键引脚
#define STATUS_LED_PIN          2
#define LEARN_BUTTON_PIN        0

// 串口引脚 (已由系统使用)
#define UART0_TX_PIN            43
#define UART0_RX_PIN            44

// SPI引脚 (Flash)
#define SPI_MOSI_PIN            35
#define SPI_MISO_PIN            37
#define SPI_CLK_PIN             36
#define SPI_CS_PIN              34

// I2C引脚 (预留)
#define I2C_SDA_PIN             8
#define I2C_SCL_PIN             9

// PWM引脚 (预留)
#define PWM_PIN_1               10
#define PWM_PIN_2               11

// ADC引脚 (预留)
#define ADC_PIN_1               1
#define ADC_PIN_2               2

// GPIO预留引脚
#define GPIO_SPARE_1            12
#define GPIO_SPARE_2            13
#define GPIO_SPARE_3            14
#define GPIO_SPARE_4            15

#endif

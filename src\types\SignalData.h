/**
 * @file SignalData.h
 * @brief 信号数据结构定义 - 完全匹配前端格式
 * 
 * 基于前端完整数据文档的SignalData结构设计
 * 支持12个字段，完全匹配前端期望格式
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef SIGNAL_DATA_H
#define SIGNAL_DATA_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <string>

struct SignalData {
    // 核心字段 - 完全匹配前端SignalData格式
    std::string id;                     // signal_12345678格式
    std::string name;                   // 信号名称
    std::string type;                   // 信号类型 (tv/ac/fan/light/other)
    std::string description;            // 信号描述
    std::string signalCode;             // 信号代码
    std::string protocol;               // 协议类型 (NEC/RC5/SONY/RAW)
    uint16_t frequency;                 // 载波频率
    std::string data;                   // 红外数据 (十六进制字符串)
    bool isLearned;                     // 是否已学习
    uint64_t created;                   // 创建时间 (13位时间戳)
    uint64_t lastSent;                  // 最后发送时间
    uint32_t sentCount;                 // 发送次数

    // 构造函数
    SignalData() : frequency(38000), isLearned(false), created(0), 
                   lastSent(0), sentCount(0) {}

    // JSON序列化 - 匹配前端格式
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["id"] = id;
        doc["name"] = name;
        doc["type"] = type;
        doc["description"] = description;
        doc["signalCode"] = signalCode;
        doc["protocol"] = protocol;
        doc["frequency"] = frequency;
        doc["data"] = data;
        doc["isLearned"] = isLearned;
        doc["created"] = created;
        doc["lastSent"] = lastSent;
        doc["sentCount"] = sentCount;
        return doc;
    }

    // JSON反序列化
    static SignalData fromJson(const JsonObject& json) {
        SignalData signal;
        signal.id = json["id"].as<std::string>();
        signal.name = json["name"].as<std::string>();
        signal.type = json["type"].as<std::string>();
        signal.description = json["description"].as<std::string>();
        signal.signalCode = json["signalCode"].as<std::string>();
        signal.protocol = json["protocol"].as<std::string>();
        signal.frequency = json["frequency"].as<uint16_t>();
        signal.data = json["data"].as<std::string>();
        signal.isLearned = json["isLearned"].as<bool>();
        signal.created = json["created"].as<uint64_t>();
        signal.lastSent = json["lastSent"].as<uint64_t>();
        signal.sentCount = json["sentCount"].as<uint32_t>();
        return signal;
    }

    // 辅助方法
    bool isValid() const {
        return !id.empty() && !name.empty() && !data.empty() && frequency > 0;
    }

    void updateAccess() {
        lastSent = millis();
        sentCount++;
    }

    // 生成JSON字符串
    std::string toJsonString() const {
        JsonDocument doc = toJson();
        std::string result;
        serializeJson(doc, result);
        return result;
    }

    // 从JSON字符串创建
    static SignalData fromJsonString(const std::string& jsonStr) {
        JsonDocument doc;
        deserializeJson(doc, jsonStr);
        return fromJson(doc.as<JsonObject>());
    }

    // 复制构造函数
    SignalData(const SignalData& other) = default;
    SignalData& operator=(const SignalData& other) = default;

    // 移动构造函数
    SignalData(SignalData&& other) noexcept = default;
    SignalData& operator=(SignalData&& other) noexcept = default;

    // 比较操作符
    bool operator==(const SignalData& other) const {
        return id == other.id;
    }

    bool operator!=(const SignalData& other) const {
        return !(*this == other);
    }

    // 用于排序
    bool operator<(const SignalData& other) const {
        return id < other.id;
    }
};

#endif

/**
 * @file TimerService.h
 * @brief 定时器服务 - 定时任务管理
 * 
 * 基于前端完整数据文档的定时任务需求
 * 支持定时信号发射、任务调度
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef TIMER_SERVICE_H
#define TIMER_SERVICE_H

#include "BaseService.h"
#include "../types/TaskData.h"
#include <vector>

class TimerService : public BaseService {
private:
    std::vector<TaskData> scheduledTasks;
    uint32_t nextTaskId;

public:
    TimerService(EventManager* em);
    virtual ~TimerService() = default;

    // BaseService接口实现
    bool init() override;
    void loop() override;
    void cleanup() override;

    // 任务管理
    std::string scheduleTask(const TaskData& task);
    bool cancelTask(const std::string& taskId);
    std::vector<TaskData> getAllTasks();
    TaskData getTask(const std::string& taskId);

private:
    void processTasks();
    void executeTask(TaskData& task);
};

#endif

/**
 * @file CacheManager.h
 * @brief 缓存管理器 - 内存缓存优化
 * 
 * 基于前端完整数据文档的性能优化需求
 * 支持LRU缓存、智能预加载、内存管理
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef CACHE_MANAGER_H
#define CACHE_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <unordered_map>
#include <list>
#include <string>

// 缓存项结构
template<typename T>
struct CacheItem {
    T data;
    uint32_t accessTime;
    uint32_t accessCount;
    size_t size;
    bool dirty;
    
    CacheItem() : accessTime(0), accessCount(0), size(0), dirty(false) {}
    CacheItem(const T& d) : data(d), accessTime(millis()), accessCount(1), 
                           size(sizeof(T)), dirty(false) {}
};

// 缓存统计
struct CacheStats {
    uint32_t hits;
    uint32_t misses;
    uint32_t evictions;
    uint32_t totalAccess;
    size_t currentSize;
    size_t maxSize;
    float hitRate;
    
    CacheStats() : hits(0), misses(0), evictions(0), totalAccess(0),
                   currentSize(0), maxSize(0), hitRate(0.0f) {}
};

template<typename T>
class CacheManager {
private:
    // LRU缓存实现
    std::unordered_map<std::string, typename std::list<std::pair<std::string, CacheItem<T>>>::iterator> cacheMap;
    std::list<std::pair<std::string, CacheItem<T>>> cacheList;
    
    // 配置
    size_t maxCacheSize;
    size_t maxMemoryUsage;
    uint32_t maxAge;
    bool enableLRU;
    
    // 统计信息
    CacheStats stats;

public:
    CacheManager(size_t maxSize = 100, size_t maxMemory = 1024 * 1024) 
        : maxCacheSize(maxSize), maxMemoryUsage(maxMemory), maxAge(3600000), enableLRU(true) {}
    
    ~CacheManager() {
        clear();
    }

    // 缓存操作
    bool put(const std::string& key, const T& value) {
        // 检查是否已存在
        auto it = cacheMap.find(key);
        if (it != cacheMap.end()) {
            // 更新现有项
            it->second->second.data = value;
            it->second->second.accessTime = millis();
            it->second->second.accessCount++;
            it->second->second.dirty = true;
            
            // 移到前面 (LRU)
            if (enableLRU) {
                cacheList.splice(cacheList.begin(), cacheList, it->second);
            }
            return true;
        }
        
        // 检查容量限制
        if (needsEviction()) {
            evictItems();
        }
        
        // 添加新项
        CacheItem<T> item(value);
        cacheList.push_front(std::make_pair(key, item));
        cacheMap[key] = cacheList.begin();
        
        updateStats(true);
        return true;
    }
    
    bool get(const std::string& key, T& value) {
        auto it = cacheMap.find(key);
        if (it != cacheMap.end()) {
            // 更新访问信息
            it->second->second.accessTime = millis();
            it->second->second.accessCount++;
            
            // 移到前面 (LRU)
            if (enableLRU) {
                cacheList.splice(cacheList.begin(), cacheList, it->second);
            }
            
            value = it->second->second.data;
            updateStats(true);
            return true;
        }
        
        updateStats(false);
        return false;
    }
    
    bool contains(const std::string& key) const {
        return cacheMap.find(key) != cacheMap.end();
    }
    
    bool remove(const std::string& key) {
        auto it = cacheMap.find(key);
        if (it != cacheMap.end()) {
            cacheList.erase(it->second);
            cacheMap.erase(it);
            stats.evictions++;
            return true;
        }
        return false;
    }
    
    void clear() {
        cacheList.clear();
        cacheMap.clear();
        resetStats();
    }
    
    // 状态查询
    size_t size() const { return cacheList.size(); }
    bool empty() const { return cacheList.empty(); }
    size_t getMemoryUsage() const { return stats.currentSize; }
    
    // 统计信息
    const CacheStats& getStats() const { return stats; }
    float getHitRate() const {
        return stats.totalAccess > 0 ? (float)stats.hits / stats.totalAccess : 0.0f;
    }
    
    JsonDocument getStatsJson() const {
        JsonDocument doc;
        doc["hits"] = stats.hits;
        doc["misses"] = stats.misses;
        doc["evictions"] = stats.evictions;
        doc["total_access"] = stats.totalAccess;
        doc["current_size"] = stats.currentSize;
        doc["max_size"] = stats.maxSize;
        doc["hit_rate"] = getHitRate();
        doc["cache_count"] = size();
        return doc;
    }
    
    // 配置管理
    void setMaxSize(size_t size) { maxCacheSize = size; }
    void setMaxMemory(size_t memory) { maxMemoryUsage = memory; }
    void setMaxAge(uint32_t age) { maxAge = age; }
    void setLRUEnabled(bool enabled) { enableLRU = enabled; }
    
    // 维护操作
    void cleanup() {
        cleanupExpiredItems();
        if (needsEviction()) {
            evictItems();
        }
    }
    
    void optimize() {
        cleanupExpiredItems();
        compactMemory();
    }

private:
    bool needsEviction() const {
        return cacheList.size() >= maxCacheSize || 
               stats.currentSize >= maxMemoryUsage;
    }
    
    void evictItems() {
        while (needsEviction() && !cacheList.empty()) {
            // 移除最后一个项 (LRU)
            auto& lastItem = cacheList.back();
            cacheMap.erase(lastItem.first);
            cacheList.pop_back();
            stats.evictions++;
        }
    }
    
    void cleanupExpiredItems() {
        uint32_t currentTime = millis();
        auto it = cacheList.begin();
        
        while (it != cacheList.end()) {
            if (currentTime - it->second.accessTime > maxAge) {
                cacheMap.erase(it->first);
                it = cacheList.erase(it);
                stats.evictions++;
            } else {
                ++it;
            }
        }
    }
    
    void compactMemory() {
        // 重新计算内存使用
        stats.currentSize = 0;
        for (const auto& item : cacheList) {
            stats.currentSize += item.second.size;
        }
    }
    
    void updateStats(bool hit) {
        if (hit) {
            stats.hits++;
        } else {
            stats.misses++;
        }
        stats.totalAccess++;
        stats.hitRate = getHitRate();
    }
    
    void resetStats() {
        stats = CacheStats();
        stats.maxSize = maxCacheSize;
    }
};

// 特化的信号缓存管理器
using SignalCacheManager = CacheManager<class SignalData>;
using ConfigCacheManager = CacheManager<JsonDocument>;
using StringCacheManager = CacheManager<std::string>;

#endif

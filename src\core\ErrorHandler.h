/**
 * @file ErrorHandler.h
 * @brief 错误处理器 - 统一错误管理和日志记录
 * 
 * 基于前端完整数据文档的错误处理需求设计
 * 支持错误分级、日志记录、WebSocket错误广播
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef ERROR_HANDLER_H
#define ERROR_HANDLER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <string>
#include <vector>
#include <queue>
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>

// 错误级别
enum class ErrorLevel {
    DEBUG = 0,
    INFO = 1,
    WARNING = 2,
    ERROR = 3,
    CRITICAL = 4
};

// 错误类别
enum class ErrorCategory {
    SYSTEM,
    NETWORK,
    HARDWARE,
    STORAGE,
    API,
    WEBSOCKET,
    SERVICE,
    USER
};

// 错误记录结构
struct ErrorRecord {
    uint32_t id;                    // 错误ID
    ErrorLevel level;               // 错误级别
    ErrorCategory category;         // 错误类别
    std::string source;             // 错误源
    std::string message;            // 错误消息
    std::string details;            // 详细信息
    uint64_t timestamp;             // 时间戳
    uint8_t coreId;                 // 发生错误的核心ID
    uint32_t freeHeap;              // 当时的可用内存
    
    ErrorRecord() : id(0), level(ErrorLevel::ERROR), category(ErrorCategory::SYSTEM),
                    timestamp(0), coreId(0), freeHeap(0) {}
};

class ErrorHandler {
private:
    // 错误记录存储
    std::queue<ErrorRecord> errorQueue;
    std::vector<ErrorRecord> errorHistory;
    
    // 线程安全
    SemaphoreHandle_t errorMutex;
    
    // 配置
    ErrorLevel logLevel;
    size_t maxHistorySize;
    size_t maxQueueSize;
    bool enableWebSocketBroadcast;
    bool enableSerialOutput;
    
    // 状态
    bool initialized;
    uint32_t nextErrorId;
    uint32_t totalErrors;
    uint32_t criticalErrors;
    
    // 错误统计
    std::vector<uint32_t> errorCountByLevel;
    std::vector<uint32_t> errorCountByCategory;

public:
    ErrorHandler();
    ~ErrorHandler();

    // 生命周期
    bool init();
    void cleanup();
    void loop();

    // 错误记录
    void logError(const std::string& source, const std::string& message, 
                  const std::string& details = "");
    void logError(ErrorCategory category, const std::string& source, 
                  const std::string& message, const std::string& details = "");
    void logError(ErrorLevel level, ErrorCategory category, 
                  const std::string& source, const std::string& message, 
                  const std::string& details = "");

    // 便捷日志方法
    void logDebug(const std::string& source, const std::string& message);
    void logInfo(const std::string& source, const std::string& message);
    void logWarning(const std::string& source, const std::string& message);
    void logCritical(const std::string& source, const std::string& message);

    // 系统错误处理
    void handleError(const std::string& source, const std::string& error);
    void handleCriticalError(const std::string& source, const std::string& error);
    void handleSystemRestart(const std::string& reason);

    // 错误查询
    std::vector<ErrorRecord> getRecentErrors(size_t count = 10);
    std::vector<ErrorRecord> getErrorsByLevel(ErrorLevel level);
    std::vector<ErrorRecord> getErrorsByCategory(ErrorCategory category);
    ErrorRecord getLastError();

    // 统计信息
    uint32_t getTotalErrors() const { return totalErrors; }
    uint32_t getCriticalErrors() const { return criticalErrors; }
    uint32_t getErrorCount(ErrorLevel level);
    uint32_t getErrorCount(ErrorCategory category);
    JsonDocument getErrorStatistics();

    // 配置管理
    void setLogLevel(ErrorLevel level) { logLevel = level; }
    ErrorLevel getLogLevel() const { return logLevel; }
    void setMaxHistorySize(size_t size) { maxHistorySize = size; }
    void setWebSocketBroadcast(bool enable) { enableWebSocketBroadcast = enable; }
    void setSerialOutput(bool enable) { enableSerialOutput = enable; }

    // 错误清理
    void clearErrorHistory();
    void clearErrorQueue();
    void clearOldErrors(uint32_t olderThanMs);

    // 状态查询
    bool isInitialized() const { return initialized; }
    size_t getQueueSize() const { return errorQueue.size(); }
    size_t getHistorySize() const { return errorHistory.size(); }

private:
    // 错误处理核心
    void processErrorQueue();
    void addErrorToHistory(const ErrorRecord& error);
    void broadcastError(const ErrorRecord& error);
    void outputToSerial(const ErrorRecord& error);
    
    // 辅助方法
    std::string levelToString(ErrorLevel level);
    std::string categoryToString(ErrorCategory category);
    std::string formatErrorMessage(const ErrorRecord& error);
    
    // 线程安全
    bool lockMutex(uint32_t timeoutMs = 1000);
    void unlockMutex();
    
    // 内存管理
    void cleanupOldErrors();
    void optimizeStorage();
    
    // WebSocket错误广播 (匹配前端error事件格式)
    JsonDocument createErrorEventData(const ErrorRecord& error);
    void sendErrorToWebSocket(const ErrorRecord& error);
};

// 全局错误处理器
extern ErrorHandler* g_errorHandler;

// 便捷宏定义
#define LOG_ERROR(source, message) if(g_errorHandler) g_errorHandler->logError(source, message)
#define LOG_WARNING(source, message) if(g_errorHandler) g_errorHandler->logWarning(source, message)
#define LOG_INFO(source, message) if(g_errorHandler) g_errorHandler->logInfo(source, message)
#define LOG_DEBUG(source, message) if(g_errorHandler) g_errorHandler->logDebug(source, message)
#define LOG_CRITICAL(source, message) if(g_errorHandler) g_errorHandler->logCritical(source, message)

#define HANDLE_ERROR(source, error) if(g_errorHandler) g_errorHandler->handleError(source, error)
#define HANDLE_CRITICAL_ERROR(source, error) if(g_errorHandler) g_errorHandler->handleCriticalError(source, error)

// 错误代码常量
namespace ErrorCodes {
    const std::string SYSTEM_INIT_FAILED = "SYSTEM_INIT_FAILED";
    const std::string NETWORK_CONNECTION_FAILED = "NETWORK_CONNECTION_FAILED";
    const std::string HARDWARE_INIT_FAILED = "HARDWARE_INIT_FAILED";
    const std::string STORAGE_READ_FAILED = "STORAGE_READ_FAILED";
    const std::string STORAGE_WRITE_FAILED = "STORAGE_WRITE_FAILED";
    const std::string API_REQUEST_FAILED = "API_REQUEST_FAILED";
    const std::string WEBSOCKET_CONNECTION_FAILED = "WEBSOCKET_CONNECTION_FAILED";
    const std::string IR_TRANSMIT_FAILED = "IR_TRANSMIT_FAILED";
    const std::string IR_RECEIVE_FAILED = "IR_RECEIVE_FAILED";
    const std::string SIGNAL_LEARNING_FAILED = "SIGNAL_LEARNING_FAILED";
    const std::string MEMORY_ALLOCATION_FAILED = "MEMORY_ALLOCATION_FAILED";
    const std::string TASK_CREATION_FAILED = "TASK_CREATION_FAILED";
    const std::string QUEUE_FULL = "QUEUE_FULL";
    const std::string TIMEOUT = "TIMEOUT";
    const std::string INVALID_PARAMETER = "INVALID_PARAMETER";
    const std::string PERMISSION_DENIED = "PERMISSION_DENIED";
    const std::string RESOURCE_BUSY = "RESOURCE_BUSY";
    const std::string OPERATION_CANCELLED = "OPERATION_CANCELLED";
}

#endif

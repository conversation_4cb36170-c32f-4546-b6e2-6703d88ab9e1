/**
 * @file OTAService.cpp
 * @brief OTA升级服务实现占位符
 */

#include "OTAService.h"

OTAService::OTAService(EventManager* em) 
    : BaseService(em, "OTAService", ServiceType::CORE0_SERVICE),
      otaEnabled(false), otaInProgress(false) {
}

bool OTAService::init() {
    logInfo("初始化OTA服务...");
    setInitialized(true);
    return true;
}

void OTAService::loop() {
    updateLoopStats();
}

void OTAService::cleanup() {
    logInfo("清理OTA服务...");
}

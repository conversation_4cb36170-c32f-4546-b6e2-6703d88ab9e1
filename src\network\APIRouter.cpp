/**
 * @file APIRouter.cpp
 * @brief API路由器实现占位符
 */

#include "APIRouter.h"

APIRouter::APIRouter(EventManager* em, AsyncWebServer* srv) 
    : BaseService(em, "APIRouter", ServiceType::CORE0_SERVICE),
      server(srv) {
}

bool APIRouter::init() {
    logInfo("初始化API路由器...");
    setupRoutes();
    setInitialized(true);
    return true;
}

void APIRouter::loop() {
    updateLoopStats();
}

void APIRouter::cleanup() {
    logInfo("清理API路由器...");
}

void APIRouter::setupRoutes() {
    if (!server) return;
    
    // 设置基本路由
    server->on("/api/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        handleGetStatus(request);
    });
    
    logInfo("API路由设置完成");
}

void APIRouter::handleGetStatus(AsyncWebServerRequest* request) {
    JsonDocument doc;
    doc["success"] = true;
    doc["data"]["uptime"] = millis() / 1000;
    doc["data"]["free_heap"] = ESP.getFreeHeap();
    doc["timestamp"] = millis();
    
    String response;
    serializeJson(doc, response);
    
    request->send(200, "application/json", response);
}

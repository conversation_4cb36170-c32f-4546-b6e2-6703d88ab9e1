/**
 * @file APITypes.h
 * @brief API类型定义 - 完全匹配前端格式
 * 
 * 基于前端完整数据文档的API响应格式设计
 * 支持统一的API响应结构和请求类型
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef API_TYPES_H
#define API_TYPES_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <string>

// API响应结构 - 完全匹配前端APIResponse格式
struct APIResponse {
    bool success;                       // 操作是否成功
    JsonVariant data;                   // 响应数据 (成功时)
    std::string error;                  // 错误信息 (失败时)
    std::string message;                // 操作消息
    uint64_t timestamp;                 // 响应时间戳

    // 构造函数
    APIResponse() : success(false), timestamp(millis()) {}

    // 创建成功响应
    static APIResponse createSuccess(const JsonVariant& responseData = JsonVariant(), 
                                   const std::string& msg = "操作成功") {
        APIResponse response;
        response.success = true;
        response.data = responseData;
        response.message = msg;
        response.timestamp = millis();
        return response;
    }

    // 创建错误响应
    static APIResponse createError(const std::string& errorMsg, 
                                 const std::string& msg = "") {
        APIResponse response;
        response.success = false;
        response.error = errorMsg;
        response.message = msg;
        response.timestamp = millis();
        return response;
    }

    // JSON序列化
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["success"] = success;
        doc["timestamp"] = timestamp;
        
        if (success) {
            doc["data"] = data;
            if (!message.empty()) {
                doc["message"] = message;
            }
        } else {
            doc["error"] = error;
            if (!message.empty()) {
                doc["message"] = message;
            }
        }
        
        return doc;
    }

    // 生成JSON字符串
    std::string toJsonString() const {
        JsonDocument doc = toJson();
        std::string result;
        serializeJson(doc, result);
        return result;
    }
};

// 学习控制请求
struct LearningRequest {
    std::string command;                // start/stop
    uint32_t timeout;                   // 超时时间
    uint64_t timestamp;                 // 请求时间戳

    LearningRequest() : timeout(30000), timestamp(0) {}

    static LearningRequest fromJson(const JsonObject& json) {
        LearningRequest request;
        request.command = json["command"].as<std::string>();
        request.timeout = json["timeout"].as<uint32_t>();
        request.timestamp = json["timestamp"].as<uint64_t>();
        return request;
    }

    bool isValid() const {
        return (command == "start" || command == "stop") && timeout > 0;
    }
};

// 信号发射请求
struct EmitRequest {
    std::string signalId;               // 信号ID
    uint8_t repeat;                     // 重复次数
    uint64_t timestamp;                 // 请求时间戳

    EmitRequest() : repeat(1), timestamp(0) {}

    static EmitRequest fromJson(const JsonObject& json) {
        EmitRequest request;
        request.signalId = json["signalId"].as<std::string>();
        request.repeat = json["repeat"].as<uint8_t>();
        request.timestamp = json["timestamp"].as<uint64_t>();
        return request;
    }

    bool isValid() const {
        return !signalId.empty() && repeat > 0 && repeat <= 10;
    }
};

// 批量请求
struct BatchRequest {
    std::string id;                     // 请求ID
    std::string endpoint;               // 端点路径
    std::string method;                 // HTTP方法
    JsonObject body;                    // 请求体

    BatchRequest() {}

    static BatchRequest fromJson(const JsonObject& json) {
        BatchRequest request;
        request.id = json["id"].as<std::string>();
        request.endpoint = json["endpoint"].as<std::string>();
        request.method = json["method"].as<std::string>();
        request.body = json["body"].as<JsonObject>();
        return request;
    }

    bool isValid() const {
        return !id.empty() && !endpoint.empty() && !method.empty();
    }
};

// 批量响应
struct BatchResponse {
    std::string id;                     // 请求ID
    bool success;                       // 是否成功
    JsonVariant data;                   // 响应数据
    std::string error;                  // 错误信息

    BatchResponse() : success(false) {}

    JsonDocument toJson() const {
        JsonDocument doc;
        doc["id"] = id;
        doc["success"] = success;
        if (success) {
            doc["data"] = data;
        } else {
            doc["error"] = error;
        }
        return doc;
    }
};

// 系统状态响应
struct SystemStatus {
    uint32_t uptime;                    // 运行时间(秒)
    float memoryUsage;                  // 内存使用率(%)
    uint16_t signalCount;               // 信号数量
    bool isLearning;                    // 是否在学习模式
    bool isEmitting;                    // 是否在发射
    std::string networkStatus;          // 网络状态
    uint32_t freeHeap;                  // 可用堆内存
    float cpuUsage;                     // CPU使用率(%)

    SystemStatus() : uptime(0), memoryUsage(0), signalCount(0), 
                     isLearning(false), isEmitting(false), 
                     networkStatus("disconnected"), freeHeap(0), cpuUsage(0) {}

    JsonDocument toJson() const {
        JsonDocument doc;
        doc["uptime"] = uptime;
        doc["memory_usage"] = memoryUsage;
        doc["signal_count"] = signalCount;
        doc["is_learning"] = isLearning;
        doc["is_emitting"] = isEmitting;
        doc["network_status"] = networkStatus;
        doc["free_heap"] = freeHeap;
        doc["cpu_usage"] = cpuUsage;
        return doc;
    }
};

// WebSocket消息类型
enum class WebSocketMessageType {
    CONNECTED,
    DISCONNECTED,
    SIGNAL_LEARNED,
    SIGNAL_SENT,
    STATUS_UPDATE,
    ERROR
};

// WebSocket消息结构
struct WebSocketMessage {
    WebSocketMessageType type;
    JsonObject payload;
    uint64_t timestamp;

    WebSocketMessage() : timestamp(millis()) {}

    static std::string typeToString(WebSocketMessageType type) {
        switch (type) {
            case WebSocketMessageType::CONNECTED: return "connected";
            case WebSocketMessageType::DISCONNECTED: return "disconnected";
            case WebSocketMessageType::SIGNAL_LEARNED: return "signal_learned";
            case WebSocketMessageType::SIGNAL_SENT: return "signal_sent";
            case WebSocketMessageType::STATUS_UPDATE: return "status_update";
            case WebSocketMessageType::ERROR: return "error";
            default: return "unknown";
        }
    }

    JsonDocument toJson() const {
        JsonDocument doc;
        doc["type"] = typeToString(type);
        doc["payload"] = payload;
        doc["timestamp"] = timestamp;
        return doc;
    }

    std::string toJsonString() const {
        JsonDocument doc = toJson();
        std::string result;
        serializeJson(doc, result);
        return result;
    }
};

#endif

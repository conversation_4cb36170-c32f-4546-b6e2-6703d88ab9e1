/**
 * @file IRControlService.h
 * @brief 红外控制服务 - 信号学习和发射控制
 * 
 * 基于前端完整数据文档的红外控制需求
 * 支持信号学习、发射、批量操作
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef IR_CONTROL_SERVICE_H
#define IR_CONTROL_SERVICE_H

#include "BaseService.h"
#include "../types/SignalData.h"
#include "../types/APITypes.h"
#include <memory>

// 前向声明
class IRTransmitter;
class IRReceiver;

// 学习状态
enum class LearningState {
    IDLE,
    LEARNING,
    COMPLETED,
    TIMEOUT,
    ERROR
};

// 发射状态
enum class EmitState {
    IDLE,
    EMITTING,
    COMPLETED,
    ERROR
};

class IRControlService : public BaseService {
private:
    std::unique_ptr<IRTransmitter> transmitter;
    std::unique_ptr<IRReceiver> receiver;
    
    // 学习状态
    LearningState learningState;
    uint32_t learningStartTime;
    uint32_t learningTimeout;
    std::string learningSignalName;
    std::string learningSignalType;
    
    // 发射状态
    EmitState emitState;
    uint32_t emitStartTime;
    std::string currentEmitSignalId;
    uint8_t emitRepeatCount;
    uint8_t emitCurrentRepeat;

public:
    IRControlService(EventManager* em);
    virtual ~IRControlService();

    // BaseService接口实现
    bool init() override;
    void loop() override;
    void cleanup() override;

    // 学习控制接口 (匹配前端API)
    bool startLearning(const LearningRequest& request);
    bool stopLearning();
    LearningState getLearningState() const { return learningState; }
    bool isLearning() const { return learningState == LearningState::LEARNING; }

    // 信号发射接口 (匹配前端API)
    bool emitSignal(const EmitRequest& request);
    bool emitSignalById(const std::string& signalId, uint8_t repeat = 1);
    EmitState getEmitState() const { return emitState; }
    bool isEmitting() const { return emitState == EmitState::EMITTING; }

    // 批量操作
    bool emitBatchSignals(const std::vector<std::string>& signalIds, 
                         uint8_t repeat = 1, uint16_t interval = 1000);

    // 状态查询
    JsonDocument getLearningStatus() const;
    JsonDocument getEmitStatus() const;
    JsonDocument getControllerStatus() const;

    // 硬件控制
    bool setIRFrequency(uint16_t frequency);
    uint16_t getIRFrequency() const;
    bool testHardware();

private:
    // 学习处理
    void processLearning();
    void onLearningCompleted(const SignalData& signal);
    void onLearningTimeout();
    void onLearningError(const std::string& error);

    // 发射处理
    void processEmitting();
    void onEmitCompleted(bool success);
    void onEmitError(const std::string& error);

    // 事件处理
    void handleLearningRequest(const EventData& event);
    void handleEmitRequest(const EventData& event);
    void handleBatchRequest(const EventData& event);

    // 状态管理
    void setLearningState(LearningState state);
    void setEmitState(EmitState state);

    // WebSocket事件广播 (匹配前端事件格式)
    void broadcastLearningStarted();
    void broadcastLearningCompleted(const SignalData& signal, uint8_t quality);
    void broadcastLearningFailed(const std::string& error);
    void broadcastSignalSent(const std::string& signalId, bool success, uint32_t duration);
    void broadcastEmitError(const std::string& signalId, const std::string& error);

    // 辅助方法
    std::string generateSignalId();
    bool validateSignalData(const SignalData& signal);
    uint8_t calculateSignalQuality(const SignalData& signal);
};

#endif

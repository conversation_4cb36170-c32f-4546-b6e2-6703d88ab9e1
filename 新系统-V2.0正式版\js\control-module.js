/**
 * R1系统 - 控制面板模块
 * 实现发射控制、批量操作、任务管理等功能
 */

class ControlModule extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'ControlModule');

    // 调试追踪器 - 详细追踪所有操作和状态变化
    this.debugTracker = {
      enabled: true,
      sessionId: `debug_${Date.now()}`,
      operations: [],

      log: (type, operation, data = {}) => {
        if (!this.debugTracker.enabled) return;

        const entry = {
          timestamp: Date.now(),
          time: new Date().toLocaleTimeString(),
          type: type,
          operation: operation,
          data: JSON.parse(JSON.stringify(data)),
          sessionId: this.debugTracker.sessionId
        };

        this.debugTracker.operations.push(entry);

        // 保持最近1000条记录
        if (this.debugTracker.operations.length > 1000) {
          this.debugTracker.operations = this.debugTracker.operations.slice(-1000);
        }
      },

      getReport: () => {
        return {
          sessionId: this.debugTracker.sessionId,
          totalOperations: this.debugTracker.operations.length,
          operations: this.debugTracker.operations.slice(-100), // 最近100条
          currentState: this.getDebugState()
        };
      }
    };

    // 模拟配置已移除 - 所有功能应通过后端API实现

    // 任务优先级定义 - 基于用户需求的完整优先级系统
    this.TASK_PRIORITIES = {
      SIGNAL_LEARNING: 4,    // 信号学习 - 最高优先级
      TIMER_TASK: 3,         // 定时任务 - 中等优先级
      SELECTED_EMIT: 2,      // 选中信号发射 - 中等优先级
      ALL_EMIT: 1            // 全部信号发射 - 最低优先级
    };

    // 控制状态 - 基于旧系统分析的完整状态管理
    this.controlState = {
      isTransmitting: false,        // 是否正在发射
      isPaused: false,              // 是否暂停
      pausedForLearning: false,     // 是否因学习而暂停
      pausedForHigherPriority: false, // 是否因高优先级任务而暂停
      emitRate: 1,                  // 发射速率 (1-10x)
      intervalRate: 1,              // 间隔速率 (1-10x)
      isLoopMode: true,             // 是否循环模式
      currentTaskId: ''             // 当前任务ID
    };

    // 发射任务状态
    this.currentEmitTask = null;
    this.emitTimeout = null;
    this.taskQueue = [];              // 任务队列
    this.pausedTasks = [];            // 暂停的任务

    // 任务历史记录
    this.taskHistory = [];
  }

  /**
   * 设置事件监听器 - 继承自BaseModule
   */
  async setupEventListeners() {
    // 系统级事件监听
    this.eventBus.on('system.refresh', () => {
      this.refresh();
    });

    // 信号管理模块通过现有的control.request.*事件进行通信

    // 信号学习模块的暂停/恢复请求
    this.eventBus.on('control.pause.request', (data) => {
      console.log('🎓 ControlModule: 收到暂停请求:', data);
      // ✅ 根源修复：现在BaseModule.emitEvent不再包装数据，直接检查source
      if (data.source === 'SignalLearning') {
        console.log('🎓 ControlModule: 确认是学习模块的暂停请求，开始处理');
        this.pauseForSignalLearning();
      } else {
        console.log('🎓 ControlModule: 暂停请求来源不是学习模块，忽略。期望: SignalLearning, 实际:', data.source || 'undefined');
      }
    });

    this.eventBus.on('control.resume.request', async (data) => {
      // ✅ 根源修复：现在BaseModule.emitEvent不再包装数据，直接检查source
      if (data.source === 'SignalLearning') {
        try {
          await this.resumeAfterSignalLearning();
        } catch (error) {
          console.error('ControlModule: 学习恢复事件处理失败:', error);
        }
      }
    });

    // 信号管理模块的请求响应
    this.eventBus.on('control.request.signals', (data) => {
      this.handleSignalsRequest(data);
    });

    this.eventBus.on('control.request.selected-signals', (data) => {
      this.handleSelectedSignalsRequest(data);
    });

    this.eventBus.on('control.request.send-signal', (data) => {
      this.handleSendSignalRequest(data);
    });

    // 批量发射请求监听
    this.eventBus.on('signal.batch.emit.request', (data) => {
      this.handleBatchEmitRequest(data);
    });

    // 定时任务执行请求监听 - 符合R1架构标准
    this.eventBus.on('timer.task.execution.request', (data) => {
      // ✅ 根源修复：现在BaseModule.emitEvent不再包装数据，直接使用
      this.handleTimerTaskRequest(data);
    });

    // ESP32事件监听
    this.eventBus.on('esp32.task.started', (data) => {
      this.onTaskStarted(data);
    });

    this.eventBus.on('esp32.task.completed', (data) => {
      this.onTaskCompleted(data);
    });

    this.eventBus.on('esp32.task.error', (data) => {
      this.onTaskError(data);
    });

    // 状态查询事件监听 - 符合架构标准的模块间通信
    this.eventBus.on('control.paused-tasks.status.request', (data) => {
      const hasPausedTasks = this.pausedTasks.length > 0;
      if (data.callback) {
        data.callback({ hasPausedTasks });
      }
    });

    this.eventBus.on('control.task.status.request', (data) => {
      const isTaskExecuting = this.currentEmitTask?.status === 'running';
      if (data.callback) {
        data.callback({ isTaskExecuting });
      }
    });

    // 调试事件响应 - 符合架构标准
    this.eventBus.on('control.debug.report.request', (data) => {
      if (data.callback && this.getFullDebugReport) {
        data.callback(this.getFullDebugReport());
      }
    });

    this.eventBus.on('control.debug.state.request', (data) => {
      if (data.callback && this.getDebugState) {
        data.callback(this.getDebugState());
      }
    });


  }

  // 信号发射处理已统一到现有的handleSendSignalRequest和handleSelectedSignalsRequest方法

  /**
   * 设置UI - 继承自BaseModule
   */
  async setupUI() {
    this.initUIElements();
    this.renderControlPanel();
  }

  /**
   * 加载模块数据 - 继承自BaseModule
   */
  async loadModuleData() {
    this.loadTaskHistory();
  }

  /**
   * 初始化UI元素 - 使用标准addEventListener
   */
  initUIElements() {
    // 使用事件委托处理控制模块内的按钮点击
    const controlModule = $('#control-module');
    if (controlModule) {
      controlModule.addEventListener('click', (e) => {
        const actionElement = e.target.closest('[data-action]');
        if (actionElement) {
          this.handleAction(actionElement.dataset.action);
          return;
        }

        // 处理其他按钮
        const target = e.target;
        if (target.id === 'createTaskBtn') {
          this.showCreateTaskDialog();
        } else if (target.id === 'taskHistoryBtn') {
          this.showTaskHistory();
        }
      });


    }
  }

  /**
   * 渲染控制面板 - 基于旧系统分析的合规实现
   */
  renderControlPanel() {
    const container = $('.control-content');
    if (!container) return;

    container.innerHTML = `
      <div class="control-panel-content">
        <!-- 发射控制区域 -->
        <div class="control-section">
          <h3>发射控制</h3>
          <div class="control-buttons">
            <button class="control-btn primary-btn" id="startEmitBtn">
              <span class="btn-icon">▶️</span>
              <span class="btn-text">开始发射</span>
            </button>
            <button class="control-btn secondary-btn" id="pauseEmitBtn" disabled>
              <span class="btn-icon">⏸️</span>
              <span class="btn-text">暂停</span>
            </button>
            <button class="control-btn secondary-btn" id="resumeEmitBtn" disabled>
              <span class="btn-icon">▶️</span>
              <span class="btn-text">恢复</span>
            </button>
            <button class="control-btn danger-btn" id="stopEmitBtn" disabled>
              <span class="btn-icon">⏹️</span>
              <span class="btn-text">停止</span>
            </button>
          </div>
        </div>

        <!-- 速率控制区域 -->
        <div class="control-section">
          <h3>速率控制</h3>
          <div class="rate-controls">
            <div class="rate-group">
              <label for="emitRate">发射速率: <span id="emitRateValue">1</span>x</label>
              <input type="range" id="emitRate" min="1" max="10" value="1" class="rate-slider">
            </div>
            <div class="rate-group">
              <label for="intervalRate">间隔速率: <span id="intervalRateValue">1</span>x</label>
              <input type="range" id="intervalRate" min="1" max="10" value="1" class="rate-slider">
            </div>
          </div>
        </div>

        <!-- 发射模式区域 -->
        <div class="control-section">
          <h3>发射模式</h3>
          <div class="mode-controls">
            <label class="radio-group">
              <input type="radio" name="emitMode" value="loop" checked>
              <span>循环模式</span>
            </label>
            <label class="radio-group">
              <input type="radio" name="emitMode" value="single">
              <span>单次模式</span>
            </label>
          </div>
        </div>


      </div>
    `;

    this.bindControlEvents();
  }

  /**
   * 绑定控制事件 - 合规实现
   */
  bindControlEvents() {
    // 缓存DOM元素
    this.elements = {
      // 发射控制按钮
      startEmitBtn: $('#startEmitBtn'),
      pauseEmitBtn: $('#pauseEmitBtn'),
      resumeEmitBtn: $('#resumeEmitBtn'),
      stopEmitBtn: $('#stopEmitBtn'),

      // 速率控制
      emitRate: $('#emitRate'),
      intervalRate: $('#intervalRate'),
      emitRateValue: $('#emitRateValue'),
      intervalRateValue: $('#intervalRateValue'),

      // 模式控制
      emitModeRadios: $$('input[name="emitMode"]')
    };

    // 绑定发射控制事件
    if (this.elements.startEmitBtn) {
      this.elements.startEmitBtn.addEventListener('click', () => {
        this.startEmit();
      });
    }

    if (this.elements.pauseEmitBtn) {
      this.elements.pauseEmitBtn.addEventListener('click', () => this.pauseEmit());
    }
    if (this.elements.resumeEmitBtn) {
      this.elements.resumeEmitBtn.addEventListener('click', () => this.resumeEmit());
    }
    if (this.elements.stopEmitBtn) {
      this.elements.stopEmitBtn.addEventListener('click', () => this.stopEmit());
    }



    // 绑定速率控制事件
    if (this.elements.emitRate) {
      this.elements.emitRate.addEventListener('input', (e) => {
        const value = parseInt(e.target.value);
        this.elements.emitRateValue.textContent = value;
        this.setEmitRate(value);
      });
    }
    if (this.elements.intervalRate) {
      this.elements.intervalRate.addEventListener('input', (e) => {
        const value = parseInt(e.target.value);
        this.elements.intervalRateValue.textContent = value;
        this.setIntervalRate(value);
      });
    }

    // 绑定模式控制事件
    this.elements.emitModeRadios.forEach(radio => {
      radio.addEventListener('change', (e) => {
        if (e.target.checked) {
          this.setEmitMode(e.target.value);
        }
      });
    });

    console.log('ControlModule: 事件绑定完成');
  }

  /**
   * 显示创建任务对话框
   */
  showCreateTaskDialog() {
    const modalContent = `
      <div class="create-task-dialog">
        <h3>创建控制任务</h3>
        <form id="createTaskForm">
          <div class="form-group">
            <label for="taskName">任务名称</label>
            <input type="text" id="taskName" class="form-input" placeholder="请输入任务名称" required>
          </div>
          <div class="form-group">
            <label for="taskType">任务类型</label>
            <select id="taskType" class="form-input">
              <option value="single">单次发射</option>
              <option value="batch">批量发射</option>
              <option value="sequence">序列发射</option>
              <option value="loop">循环发射</option>
            </select>
          </div>
          <div class="form-group">
            <label for="taskSignals">选择信号</label>
            <div id="taskSignals" class="signal-selector">
              <!-- 信号选择器将在这里动态生成 -->
            </div>
          </div>
          <div class="form-group">
            <label for="taskDescription">任务描述</label>
            <textarea id="taskDescription" class="form-input" rows="3" placeholder="任务描述（可选）"></textarea>
          </div>
          <div class="form-actions">
            <button type="button" class="btn secondary" data-action="close-modal">取消</button>
            <button type="submit" class="btn primary">创建任务</button>
          </div>
        </form>
      </div>
    `;

    // 通过事件系统显示模态框
    this.emitEvent('system.modal.show', {
      content: modalContent,
      title: '创建控制任务'
    });
    this.loadSignalsForTaskCreation();
  }

  /**
   * 为任务创建加载信号列表
   */
  loadSignalsForTaskCreation() {
    // 通过事件系统请求信号列表
    this.emitEvent('control.request.signals', {
      purpose: 'task-creation',
      callback: (signals) => {
        this.renderTaskSignals(signals);
      }
    });
  }

  /**
   * 渲染任务信号列表
   * @param {Array} signals - 信号数组
   */
  renderTaskSignals(signals) {
    const container = $('#taskSignals');
    if (!container) return;

    if (!signals || signals.length === 0) {
      container.innerHTML = '<p class="no-signals">暂无可用信号</p>';
      return;
    }

    container.innerHTML = signals.map(signal => `
      <label class="signal-option">
        <input type="checkbox" value="${signal.id}" name="selectedSignals">
        <span class="signal-name">${signal.name}</span>
        <span class="signal-type">${this.getTypeEmoji(signal.type)} ${this.getTypeName(signal.type)}</span>
      </label>
    `).join('');
  }



  // 工具方法
  getTypeEmoji(type) {
    const emojis = { tv: '📺', ac: '❄️', fan: '🌀', light: '💡', other: '📱' };
    return emojis[type] || '📱';
  }

  getTypeName(type) {
    const names = { tv: '电视', ac: '空调', fan: '风扇', light: '灯光', other: '其他' };
    return names[type] || '其他';
  }

  // ESP32事件处理方法
  onTaskStarted(data) {
    this.performance.operationCount++;
    this.performance.lastOperation = 'ESP32任务启动';
  }

  onTaskCompleted(data) {
    this.performance.operationCount++;
    this.performance.lastOperation = 'ESP32任务完成';
  }

  onTaskError(data) {
    this.performance.errorCount++;
    this.performance.lastOperation = 'ESP32任务错误';
  }

  /**
   * 处理定时任务执行请求 - 符合R1架构标准
   */
  async handleTimerTaskRequest(requestData) {
    try {
      const { task, priority, requestTime, source } = requestData;

      console.log('🎮 ControlModule: 收到定时任务执行请求:', {
        taskId: task.id,
        taskName: task.name,
        priority: priority,
        source: source,
        requestTime: new Date(requestTime).toLocaleString()
      });

      // 发布任务开始事件
      this.emitEvent('timer.task.execution.started', {
        taskId: task.id,
        taskName: task.name,
        startTime: Date.now()
      });

      // 获取要发射的信号
      let signalsToEmit = [];
      if (task.selectedSignalIds && task.selectedSignalIds.length > 0) {
        // 发射选中的信号
        signalsToEmit = await this.getSignalsByIds(task.selectedSignalIds);
      } else {
        // 发射全部信号
        signalsToEmit = await this.getAllSignals();
      }

      if (signalsToEmit.length === 0) {
        throw new Error('没有可发射的信号');
      }

      // 创建定时发射任务 - 使用正确的优先级
      const emitTask = this.createEmitTask(signalsToEmit, 'timer', {
        taskName: task.name,
        timerTaskId: task.id,
        requestTime: requestTime,
        source: source
      });
      emitTask.timerTaskId = task.id;
      emitTask.timerTaskName = task.name;

      console.log('🎮 ControlModule: 创建定时发射任务:', {
        taskId: emitTask.id,
        taskName: emitTask.name,
        priority: emitTask.priority,
        signalsCount: emitTask.signals.length
      });

      // 启动发射任务
      const result = await this.startEmitTask(emitTask);

      console.log('ControlModule: 定时任务发射启动结果:', result);

    } catch (error) {
      console.error('ControlModule: 处理定时任务请求失败:', error);

      // 发布任务失败事件
      this.emitEvent('timer.task.execution.failed', {
        taskId: task?.id,
        error: error.message,
        executionTime: Date.now()
      });
    }
  }

  /**
   * 根据ID获取信号列表
   */
  async getSignalsByIds(signalIds) {
    return new Promise((resolve) => {
      this.emitEvent('signal.request.by-ids', {
        signalIds: signalIds,
        source: 'ControlModule',
        callback: (signals) => {
          resolve(signals || []);
        }
      });
    });
  }

  /**
   * 获取所有信号
   */
  async getAllSignals() {
    return new Promise((resolve) => {
      this.emitEvent('signal.request.all', {
        source: 'ControlModule',
        purpose: 'timer-execution',
        callback: (signals) => {
          resolve(signals || []);
        }
      });
    });
  }

  /**
   * 处理信号学习模块的暂停请求 - 使用正确的优先级系统
   */
  pauseForSignalLearning() {
    try {
      console.log('🎓 ControlModule: 收到学习暂停请求（最高优先级），当前状态:', {
        isTransmitting: this.controlState.isTransmitting,
        isPaused: this.controlState.isPaused,
        currentTask: this.currentEmitTask?.name || 'none',
        pausedForLearning: this.controlState.pausedForLearning
      });

      // 设置学习暂停状态 - 这将阻止新任务启动
      this.controlState.pausedForLearning = true;

      // 如果有正在执行的任务，使用优先级系统暂停它
      if (this.currentEmitTask && this.currentEmitTask.status === 'running') {
        console.log('🎓 ControlModule: 学习模式暂停正在执行的任务:', this.currentEmitTask.name);

        // 保存当前任务信息用于事件发送
        const pausedTask = this.currentEmitTask;

        // 停止发射循环
        if (this.emitTimeout) {
          clearTimeout(this.emitTimeout);
          this.emitTimeout = null;
        }

        // 保存任务状态到暂停队列（按优先级排序）
        this.currentEmitTask.status = 'paused';
        this.currentEmitTask.pausedForHigherPriority = true;

        // 将暂停的任务插入到正确的位置（按优先级排序）
        this.insertTaskByPriority(this.pausedTasks, this.currentEmitTask);

        // 清除当前任务状态
        this.currentEmitTask = null;
        this.controlState.isTransmitting = false;
        this.controlState.isPaused = false;

        // 发送学习暂停事件给状态显示模块 - 确保数据结构完整
        this.emitEvent('control.emit.paused', {
          task: {
            id: pausedTask.id,
            name: pausedTask.name,
            type: pausedTask.type,
            signals: pausedTask.signals,
            priority: pausedTask.priority
          },
          reason: 'learning_mode',
          pausedForLearning: true
        });

        console.log('🎓 ControlModule: 任务已暂停，学习模式激活');
      } else {
        console.log('🎓 ControlModule: 无正在执行的任务，学习模式直接激活');
      }

      // 更新UI状态
      this.updateControlUI();

      console.log('🎓 ControlModule: 学习暂停完成，新状态:', {
        pausedForLearning: this.controlState.pausedForLearning,
        isTransmitting: this.controlState.isTransmitting,
        isPaused: this.controlState.isPaused,
        pausedTasksCount: this.pausedTasks.length
      });

      this.performance.operationCount++;
      this.performance.lastOperation = '信号学习暂停';
    } catch (error) {
      this.performance.errorCount++;
      console.error('ControlModule: 信号学习暂停失败:', error);
    }
  }



  /**
   * 处理信号学习模块的恢复请求 - 使用正确的优先级系统恢复
   */
  async resumeAfterSignalLearning() {
    try {
      console.log('🎓 ControlModule: 收到学习恢复请求，当前状态:', {
        isTransmitting: this.controlState.isTransmitting,
        isPaused: this.controlState.isPaused,
        pausedForLearning: this.controlState.pausedForLearning,
        pausedTasksCount: this.pausedTasks.length,
        queuedTasksCount: this.taskQueue.length
      });

      if (this.controlState.pausedForLearning) {
        // 清除学习暂停标记
        this.controlState.pausedForLearning = false;

        console.log('🎓 ControlModule: 学习模式结束，开始恢复任务');

        // 按优先级恢复任务：先恢复暂停的任务，再处理队列中的任务
        let taskResumed = false;

        // 1. 尝试恢复最高优先级的暂停任务
        if (this.pausedTasks.length > 0) {
          taskResumed = await this.resumePausedTasks();
          console.log('🎓 ControlModule: 恢复暂停任务结果:', taskResumed);
        }

        // 2. 如果没有暂停的任务，处理队列中的任务
        if (!taskResumed && this.taskQueue.length > 0) {
          taskResumed = await this.processNextQueuedTask();
          console.log('🎓 ControlModule: 处理队列任务结果:', taskResumed);
        }

        // 3. 如果没有任何任务需要处理，系统回到待机状态
        if (!taskResumed) {
          this.controlState.isTransmitting = false;
          this.controlState.isPaused = false;
          this.currentEmitTask = null;
          console.log('🎓 ControlModule: 学习结束，无待处理任务，系统回到待机状态');
        }

        // 更新UI
        this.updateControlUI();

        console.log('🎓 ControlModule: 学习恢复处理完成');
      } else {
        console.log('🎓 ControlModule: 系统未处于学习暂停状态，无需恢复');
      }

      this.performance.operationCount++;
      this.performance.lastOperation = '信号学习恢复';
    } catch (error) {
      this.performance.errorCount++;
      console.error('ControlModule: 信号学习恢复失败:', error);
    }
  }

  /**
   * 处理信号请求
   */
  handleSignalsRequest(data) {
    try {
      // 通过事件向信号管理模块请求信号列表
      this.emitEvent('signal.request.all', {
        source: 'ControlModule',
        purpose: data.purpose,
        callback: (signals) => {
          if (data.callback) {
            data.callback(signals);
          }
        }
      });

      this.performance.operationCount++;
      this.performance.lastOperation = '请求信号列表';
    } catch (error) {
      this.performance.errorCount++;
      console.error('ControlModule: 处理信号请求失败:', error);
    }
  }

  /**
   * 处理选中信号请求 - 支持数据请求和批量发射执行 - 支持追加信号功能
   */
  async handleSelectedSignalsRequest(data) {
    try {
      // 如果是来自信号管理模块的批量发射请求
      if (data.source === 'SignalManager' && data.signalIds) {

        // 检查是否有正在执行的批量发射任务
        const isRunningBatchTask = this.currentEmitTask &&
                                  this.currentEmitTask.type === 'batch' &&
                                  this.currentEmitTask.status === 'running';



        if (isRunningBatchTask) {
          // 追加模式：将新信号追加到当前任务队列

          // 通过事件获取信号详细信息
          this.emitEvent('signal.request.by-ids', {
            signalIds: data.signalIds,
            source: 'ControlModule',
            callback: async (signals) => {
              if (signals && signals.length > 0) {
                this.appendSignalsToCurrentTask(signals);
              }
            }
          });
        } else {
          // 正常模式：创建新的发射任务

          // 通过事件获取信号详细信息
          this.emitEvent('signal.request.by-ids', {
            signalIds: data.signalIds,
            source: 'ControlModule',
            callback: async (signals) => {
              if (signals && signals.length > 0) {
                const task = this.createEmitTask(signals, 'batch', {
                  source: data.source,
                  priority: 'medium'
                });

                // 启动发射任务
                await this.startEmitTask(task);
              }
            }
          });
        }

        return;
      }

      // 原有的数据请求逻辑
      this.emitEvent('signal.request.selected', {
        source: 'ControlModule',
        purpose: data.purpose,
        callback: (signals) => {
          if (data.callback) {
            data.callback(signals);
          }
        }
      });

      this.performance.operationCount++;
      this.performance.lastOperation = '请求选中信号';
    } catch (error) {
      this.performance.errorCount++;
      console.error('ControlModule: 处理选中信号请求失败:', error);
    }
  }

  /**
   * 处理发送信号请求 - 支持数据请求和发射执行
   */
  async handleSendSignalRequest(data) {
    try {
      // 如果是来自信号管理模块的发射请求
      if (data.source === 'SignalManager') {
        console.log('🎮 [DEBUG] ControlModule.handleSendSignalRequest() - 来自信号管理模块:', data);

        // 通过事件获取信号详细信息
        console.log('📤 [DEBUG] ControlModule: 发送 signal.request.by-ids 事件获取单个信号详情');

        this.emitEvent('signal.request.by-ids', {
          signalIds: [data.signalId],
          source: 'ControlModule',
          callback: async (signals) => {
            console.log(`🔄 [DEBUG] ControlModule: 收到信号回调，信号数量: ${signals?.length || 0}`);
            if (signals && signals.length > 0) {
              const signal = signals[0];
              console.log(`✅ [DEBUG] ControlModule: 获得信号详情: ${signal.name}, 创建单个发射任务`);

              const task = this.createEmitTask([signal], 'single', {
                source: data.source,
                priority: 'medium'
              });

              console.log(`🚀 [DEBUG] ControlModule: 启动单个信号发射任务: ${task.name}`);
              // 启动发射任务
              await this.startEmitTask(task);
            } else {
              console.warn(`⚠️ [DEBUG] ControlModule: 未获得信号详情，无法创建任务`);
            }
          }
        });

        return;
      }

      // 原有的数据请求逻辑
      this.emitEvent('signal.request.send', {
        signalId: data.signalId,
        source: 'ControlModule',
        callback: (success) => {
          if (data.callback) {
            data.callback(success);
          }
        }
      });

      this.performance.operationCount++;
      this.performance.lastOperation = '请求发送信号';
    } catch (error) {
      this.performance.errorCount++;
      console.error('ControlModule: 处理发送信号请求失败:', error);
    }
  }

  // ==================== 核心控制方法 - 基于旧系统分析 ====================

  /**
   * 开始发射 - 完整实现
   */
  async startEmit() {
    this.debugTracker.log('USER_ACTION', '用户点击开始发射按钮', {
      currentState: this.getDebugState(),
      timestamp: Date.now()
    });

    console.log('🚀 ControlModule: startEmit() 方法被调用');

    try {
      if (this.controlState.isTransmitting) {
        this.debugTracker.log('STATE_CHANGE', '发射已在进行中，拒绝新请求', {
          currentState: this.controlState
        });
        console.warn('ControlModule: 已在发射中');
        return { success: false, message: '已在发射中' };
      }

      // 获取要发射的信号（全部信号）
      const signalsToEmit = await this.getAllSignalsForEmit();
      this.debugTracker.log('TASK_OPERATION', '获取全部信号完成', {
        signalCount: signalsToEmit?.length || 0,
        signalNames: signalsToEmit?.slice(0, 5).map(s => s.name) || []
      });

      if (!signalsToEmit || signalsToEmit.length === 0) {
        this.debugTracker.log('ERROR', '没有可用信号', {});
        this.handleError(new Error('没有可用信号'), '发射控制');
        return { success: false, message: '没有可用信号' };
      }

      // 创建发射任务
      const task = this.createEmitTask(signalsToEmit, 'all');
      this.debugTracker.log('TASK_OPERATION', '创建全部发射任务', {
        taskId: task.id,
        taskName: task.name,
        taskType: task.type,
        priority: task.priority,
        isManual: task.isManual,
        signalCount: task.signals?.length,
        isLoopMode: task.isLoopMode
      });

      // 启动任务
      const result = await this.startEmitTask(task);
      this.debugTracker.log('TASK_OPERATION', '启动任务完成', {
        result: result,
        finalState: this.getDebugState()
      });

      return result;

    } catch (error) {
      this.debugTracker.log('ERROR', '开始发射失败', {
        error: error.message,
        stack: error.stack
      });
      console.error('ControlModule: 开始发射失败:', error);
      this.performance.errorCount++;
      this.handleError(error, '发射控制');
      return { success: false, error: error.message };
    }
  }

  /**
   * 创建发射任务 - 基于任务类型自动设置正确的优先级
   */
  createEmitTask(signals, type = 'all', source = null) {
    // 根据任务类型和来源自动确定优先级
    let priority;
    let taskName;
    let isManual = false; // 是否为手动操作

    switch (type) {
      case 'timer':
        priority = this.TASK_PRIORITIES.TIMER_TASK;
        taskName = source?.taskName || '定时任务';
        isManual = false; // 定时任务是自动的
        break;
      case 'selected':
        priority = this.TASK_PRIORITIES.SELECTED_EMIT;
        taskName = '选中信号发射';
        isManual = true; // 选中信号发射是手动的
        break;
      case 'single':
        priority = this.TASK_PRIORITIES.SELECTED_EMIT;
        taskName = '单个信号发射';
        isManual = true; // 单个信号发射是手动的
        break;
      case 'batch':
        priority = this.TASK_PRIORITIES.SELECTED_EMIT;
        taskName = '批量信号发射';
        isManual = true; // 批量信号发射是手动的
        break;
      case 'all':
      default:
        priority = this.TASK_PRIORITIES.ALL_EMIT;
        taskName = '全部信号发射';
        isManual = true; // 全部信号发射是手动的
        break;
    }

    return {
      id: `emit-task-${Date.now()}`,
      type: type, // 'all' | 'selected' | 'single' | 'batch' | 'timer'
      priority: priority, // 数字优先级，越大越优先
      isManual: isManual, // 是否为手动操作
      name: taskName,
      source: source, // 任务来源信息
      signals: signals,
      isLoopMode: this.controlState.isLoopMode,
      emitRate: this.controlState.emitRate,
      intervalRate: this.controlState.intervalRate,
      startTime: Date.now(),
      currentSignalIndex: 0,
      currentRepeatCount: 0,
      emitCount: 0,
      status: 'pending'
    };
  }

  /**
   * 启动发射任务
   */
  async startEmitTask(task) {
    try {
      // 首先检查学习暂停状态 - 学习模式具有最高优先级
      if (this.controlState.pausedForLearning) {
        console.log(`🎓 ControlModule: 任务 ${task.name} 被学习模式阻止，加入队列等待`);
        this.insertTaskByPriority(this.taskQueue, task);
        return { success: true, queued: true, taskId: task.id, reason: 'learning_mode' };
      }

      // 检查任务优先级冲突
      if (this.controlState.isTransmitting) {
        return await this.handleTaskPriority(task);
      }

      // 更新状态
      this.controlState.isTransmitting = true;
      this.controlState.isPaused = false;
      this.controlState.currentTaskId = task.id;

      // 设置当前任务
      this.currentEmitTask = task;
      this.currentEmitTask.status = 'running';

      // 更新UI
      this.updateControlUI();

      // 开始发射循环
      this.startEmitLoop();

      // 发布事件 - 确保数据结构完整

      // 发送完整的任务信息，确保监控模块能正确解析
      this.emitEvent('control.emit.started', {
        task: {
          id: this.currentEmitTask.id,
          name: this.currentEmitTask.name,
          type: this.currentEmitTask.type,
          signals: this.currentEmitTask.signals,
          priority: this.currentEmitTask.priority,
          isManual: this.currentEmitTask.isManual,
          startTime: this.currentEmitTask.startTime,
          isLoopMode: this.currentEmitTask.isLoopMode  // ✅ 添加循环模式信息
        }
      });

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '开始发射';

      return { success: true, taskId: task.id };

    } catch (error) {
      this.performance.errorCount++;
      throw error;
    }
  }

  /**
   * 处理任务优先级 - 基于数字优先级的完整优先级系统
   */
  async handleTaskPriority(newTask) {
    const currentTask = this.currentEmitTask;

    console.log(`🔄 ControlModule: 处理任务优先级冲突`, {
      newTask: { type: newTask.type, priority: newTask.priority, name: newTask.name, isManual: newTask.isManual },
      currentTask: { type: currentTask.type, priority: currentTask.priority, name: currentTask.name }
    });

    // 手动操作具有最高优先级，可以中断任何自动任务
    if (newTask.isManual) {
      console.log(`👤 ControlModule: 手动操作 ${newTask.name} 中断当前任务 ${currentTask.name}`);

      // 保存当前任务状态到暂停队列
      this.pauseCurrentTaskForHigherPriority();

      // 重置当前任务并启动新任务
      this.currentEmitTask = newTask;
      this.currentEmitTask.status = 'running';
      this.controlState.currentTaskId = newTask.id;
      this.controlState.isTransmitting = true;
      this.controlState.isPaused = false;

      // 更新UI
      this.updateControlUI();

      // 开始发射循环
      this.startEmitLoop();

      // 发布事件
      this.emitEvent('control.emit.started', {
        task: {
          ...this.currentEmitTask,
          isLoopMode: this.currentEmitTask.isLoopMode  // ✅ 确保包含循环模式信息
        }
      });

      return { success: true, taskId: newTask.id };
    }

    // 自动任务之间的优先级比较
    if (newTask.priority > currentTask.priority) {
      // 新任务优先级更高，暂停当前任务
      console.log(`⬆️ ControlModule: 高优先级任务 ${newTask.name}(${newTask.priority}) 暂停当前任务 ${currentTask.name}(${currentTask.priority})`);

      // 保存当前任务状态到暂停队列
      this.pauseCurrentTaskForHigherPriority();

      // 重置当前任务并启动新任务
      this.currentEmitTask = newTask;
      this.currentEmitTask.status = 'running';
      this.controlState.currentTaskId = newTask.id;

      // 更新UI
      this.updateControlUI();

      // 开始发射循环
      this.startEmitLoop();

      // 发布事件
      this.emitEvent('control.emit.started', {
        task: {
          ...this.currentEmitTask,
          isLoopMode: this.currentEmitTask.isLoopMode  // ✅ 确保包含循环模式信息
        }
      });

      return { success: true, taskId: newTask.id };
    } else if (newTask.priority === currentTask.priority) {
      // 优先级相等，新任务按优先级加入队列等待
      this.insertTaskByPriority(this.taskQueue, newTask);
      console.log(`⏸️ ControlModule: 同优先级任务 ${newTask.name} 已加入队列，当前队列长度: ${this.taskQueue.length}`);

      return { success: true, queued: true, taskId: newTask.id };
    } else {
      // 新任务优先级更低，按优先级加入队列
      this.insertTaskByPriority(this.taskQueue, newTask);
      console.log(`⬇️ ControlModule: 低优先级任务 ${newTask.name}(${newTask.priority}) 已加入队列，当前队列长度: ${this.taskQueue.length}`);

      return { success: true, queued: true, taskId: newTask.id };
    }
  }

  /**
   * 暂停当前任务 - 通用暂停方法
   */
  pauseCurrentTask() {
    if (this.currentEmitTask && this.currentEmitTask.status === 'running') {
      // 停止发射循环
      if (this.emitTimeout) {
        clearTimeout(this.emitTimeout);
        this.emitTimeout = null;
      }

      // 保存任务状态
      this.currentEmitTask.status = 'paused';
      this.pausedTasks.push(this.currentEmitTask);

      console.log(`ControlModule: 任务 ${this.currentEmitTask.name} 已暂停`);
    }
  }

  /**
   * 因高优先级任务暂停当前任务
   */
  pauseCurrentTaskForHigherPriority() {
    if (this.currentEmitTask && this.currentEmitTask.status === 'running') {
      // 停止发射循环
      if (this.emitTimeout) {
        clearTimeout(this.emitTimeout);
        this.emitTimeout = null;
      }

      // 保存任务状态到暂停队列（按优先级排序）
      this.currentEmitTask.status = 'paused';
      this.currentEmitTask.pausedForHigherPriority = true;

      // 将暂停的任务插入到正确的位置（按优先级排序）
      this.insertTaskByPriority(this.pausedTasks, this.currentEmitTask);

      // 标记控制状态
      this.controlState.pausedForHigherPriority = true;

      console.log(`⏸️ ControlModule: 任务 ${this.currentEmitTask.name}(${this.currentEmitTask.priority}) 因高优先级任务暂停`);

      // 发布暂停事件 - 确保数据结构完整
      this.emitEvent('control.emit.paused', {
        task: {
          id: this.currentEmitTask.id,
          name: this.currentEmitTask.name,
          type: this.currentEmitTask.type,
          signals: this.currentEmitTask.signals,
          priority: this.currentEmitTask.priority
        },
        reason: 'higher_priority'
      });
    }
  }

  /**
   * 按优先级将任务插入到队列中
   */
  insertTaskByPriority(taskArray, task) {
    // 找到正确的插入位置（优先级从高到低排序）
    let insertIndex = 0;
    for (let i = 0; i < taskArray.length; i++) {
      if (task.priority > taskArray[i].priority) {
        insertIndex = i;
        break;
      }
      insertIndex = i + 1;
    }

    taskArray.splice(insertIndex, 0, task);
    console.log(`📋 ControlModule: 任务 ${task.name} 按优先级插入到位置 ${insertIndex}`);
  }

  /**
   * 恢复暂停的任务 - 按优先级恢复最高优先级的任务
   */
  async resumePausedTasks() {
    if (this.pausedTasks.length > 0) {
      // 获取最高优先级的暂停任务（数组已按优先级排序）
      const taskToResume = this.pausedTasks.shift();
      console.log(`🔄 ControlModule: 恢复任务 ${taskToResume.name}(${taskToResume.priority})`);

      // 恢复任务
      this.debugTracker.log('TASK_OPERATION', '开始恢复暂停的任务', {
        taskToResume: {
          id: taskToResume.id,
          name: taskToResume.name,
          type: taskToResume.type,
          priority: taskToResume.priority,
          currentSignalIndex: taskToResume.currentSignalIndex,
          totalSignals: taskToResume.signals?.length,
          isLoopMode: taskToResume.isLoopMode,
          emitCount: taskToResume.emitCount,
          status: taskToResume.status
        },
        beforeState: this.getDebugState()
      });

      this.currentEmitTask = taskToResume;
      this.currentEmitTask.status = 'running';
      this.currentEmitTask.pausedForHigherPriority = false;
      this.controlState.currentTaskId = taskToResume.id;
      this.controlState.pausedForHigherPriority = false;
      this.controlState.isTransmitting = true;
      this.controlState.isPaused = false;

      this.debugTracker.log('STATE_CHANGE', '任务恢复状态更新完成', {
        afterState: this.getDebugState(),
        currentTask: {
          id: this.currentEmitTask.id,
          name: this.currentEmitTask.name,
          currentSignalIndex: this.currentEmitTask.currentSignalIndex,
          totalSignals: this.currentEmitTask.signals?.length
        }
      });

      console.log(`🔄 ControlModule: 恢复任务 ${taskToResume.name}(${taskToResume.priority})，当前进度: ${taskToResume.currentSignalIndex}/${taskToResume.signals?.length}`);

      // 确保任务状态完全正确
      if (!taskToResume.signals || taskToResume.signals.length === 0) {
        this.debugTracker.log('ERROR', '恢复的任务没有信号', {
          taskId: taskToResume.id,
          taskName: taskToResume.name
        });
        console.warn('ControlModule: 恢复的任务没有信号，完成任务');
        this.completeCurrentTask();
        return false;
      }

      // 更新UI显示
      this.updateControlUI();

      // 继续发射循环
      this.startEmitLoop();

      // 发布恢复事件 - 确保数据结构完整
      this.emitEvent('control.emit.resumed', {
        task: {
          id: taskToResume.id,
          name: taskToResume.name,
          type: taskToResume.type,
          signals: taskToResume.signals,
          priority: taskToResume.priority
        },
        reason: 'priority_restored'
      });

      // 发布任务开始事件，确保状态显示模块正确更新任务信息
      this.emitEvent('control.emit.started', {
        task: {
          id: taskToResume.id,
          name: taskToResume.name,
          type: taskToResume.type,
          signals: taskToResume.signals,
          priority: taskToResume.priority,
          isManual: taskToResume.isManual,
          startTime: taskToResume.startTime,
          isLoopMode: taskToResume.isLoopMode  // ✅ 确保包含循环模式信息
        }
      });

      return true;
    }
    return false;
  }

  /**
   * 处理下一个队列任务
   */
  async processNextQueuedTask() {
    if (this.taskQueue.length > 0) {
      const nextTask = this.taskQueue.shift();
      console.log(`🔄 ControlModule: 处理队列任务 ${nextTask.name}(${nextTask.priority})`);

      // 直接启动任务，避免递归调用
      this.currentEmitTask = nextTask;
      this.currentEmitTask.status = 'running';
      this.controlState.currentTaskId = nextTask.id;
      this.controlState.isTransmitting = true;
      this.controlState.isPaused = false;

      // 更新UI
      this.updateControlUI();

      // 开始发射循环
      this.startEmitLoop();

      // 发布事件
      this.emitEvent('control.emit.started', {
        task: {
          ...this.currentEmitTask,
          isLoopMode: this.currentEmitTask.isLoopMode  // ✅ 确保包含循环模式信息
        }
      });

      return true;
    }
    return false;
  }

  /**
   * 暂停发射
   */
  pauseEmit() {
    try {
      if (!this.controlState.isTransmitting) {
        console.warn('ControlModule: 未在发射中');
        return { success: false, message: '未在发射中' };
      }

      if (this.controlState.isPaused) {
        console.warn('ControlModule: 已暂停');
        return { success: false, message: '已暂停' };
      }

      // 更新状态
      this.controlState.isPaused = true;

      // 停止当前定时器
      if (this.emitTimeout) {
        clearTimeout(this.emitTimeout);
        this.emitTimeout = null;
      }

      // 更新任务状态
      if (this.currentEmitTask) {
        this.currentEmitTask.status = 'paused';
      }

      // 更新UI
      this.updateControlUI();

      console.log('ControlModule: 发射任务已暂停');

      // 发布事件 - 确保数据结构完整
      this.emitEvent('control.emit.paused', {
        task: {
          id: this.currentEmitTask.id,
          name: this.currentEmitTask.name,
          type: this.currentEmitTask.type,
          signals: this.currentEmitTask.signals,
          priority: this.currentEmitTask.priority
        }
      });

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '暂停发射';

      return { success: true };
    } catch (error) {
      console.error('ControlModule: 暂停发射失败:', error);
      this.performance.errorCount++;
      this.handleError(error, '发射控制');
      return { success: false, error: error.message };
    }
  }

  /**
   * 恢复发射
   */
  resumeEmit() {
    try {
      if (!this.controlState.isTransmitting) {
        console.warn('ControlModule: 未在发射中');
        return { success: false, message: '未在发射中' };
      }

      if (!this.controlState.isPaused) {
        console.warn('ControlModule: 未暂停');
        return { success: false, message: '未暂停' };
      }

      // 更新状态
      this.controlState.isPaused = false;

      // 更新任务状态
      if (this.currentEmitTask) {
        this.currentEmitTask.status = 'running';
      }

      // 恢复发射循环
      this.startEmitLoop();

      // 更新UI
      this.updateControlUI();

      console.log('ControlModule: 发射已恢复');

      // 发布事件 - 确保数据结构完整
      this.emitEvent('control.emit.resumed', {
        task: {
          id: this.currentEmitTask.id,
          name: this.currentEmitTask.name,
          type: this.currentEmitTask.type,
          signals: this.currentEmitTask.signals,
          priority: this.currentEmitTask.priority
        }
      });

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '恢复发射';

      return { success: true };
    } catch (error) {
      console.error('ControlModule: 恢复发射失败:', error);
      this.performance.errorCount++;
      this.handleError(error, '发射控制');
      return { success: false, error: error.message };
    }
  }

  /**
   * 停止发射
   */
  stopEmit() {
    try {
      if (!this.controlState.isTransmitting) {
        console.warn('ControlModule: 未在发射中');
        return { success: false, message: '未在发射中' };
      }

      // 更新状态
      this.controlState.isTransmitting = false;
      this.controlState.isPaused = false;

      // 停止所有定时器
      if (this.emitTimeout) {
        clearTimeout(this.emitTimeout);
        this.emitTimeout = null;
      }

      // 更新任务状态
      const completedTask = this.currentEmitTask;
      if (completedTask) {
        completedTask.status = 'stopped';
        completedTask.endTime = Date.now();
        completedTask.duration = completedTask.endTime - completedTask.startTime;
      }

      // 清空当前任务
      this.currentEmitTask = null;

      // 更新UI
      this.updateControlUI();

      console.log('ControlModule: 发射已停止');

      // 发布事件 - 确保数据结构完整
      this.emitEvent('control.emit.stopped', {
        task: {
          id: completedTask.id,
          name: completedTask.name,
          type: completedTask.type,
          signals: completedTask.signals,
          priority: completedTask.priority,
          endTime: completedTask.endTime,
          duration: completedTask.duration
        }
      });

      // 添加任务到历史记录
      if (completedTask) {
        this.addTaskToHistory(completedTask);
      }

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '停止发射';

      return { success: true };
    } catch (error) {
      console.error('ControlModule: 停止发射失败:', error);
      this.performance.errorCount++;
      this.handleError(error, '发射控制');
      return { success: false, error: error.message };
    }
  }

  /**
   * 速率控制方法
   */
  setEmitRate(rate) {
    try {
      this.controlState.emitRate = rate;

      // 互斥关系：设置发射速率时，间隔速率重置为1
      this.controlState.intervalRate = 1;
      if (this.elements.intervalRate) {
        this.elements.intervalRate.value = '1';
        this.elements.intervalRateValue.textContent = '1';
      }

      // 如果正在发射，重新计算延迟
      if (this.controlState.isTransmitting && !this.controlState.isPaused) {
        this.restartEmitLoop();
      }

      console.log(`ControlModule: 发射速率设置为: ${rate}x，间隔速率重置为: 1x`);

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '设置发射速率';

      return { success: true, rate: rate };
    } catch (error) {
      console.error('ControlModule: 设置发射速率失败:', error);
      this.performance.errorCount++;
      this.handleError(error, '速率控制');
      return { success: false, error: error.message };
    }
  }

  setIntervalRate(rate) {
    try {
      this.controlState.intervalRate = rate;

      // 互斥关系：设置间隔速率时，发射速率重置为1
      this.controlState.emitRate = 1;
      if (this.elements.emitRate) {
        this.elements.emitRate.value = '1';
        this.elements.emitRateValue.textContent = '1';
      }

      // 如果正在发射，重新计算延迟
      if (this.controlState.isTransmitting && !this.controlState.isPaused) {
        this.restartEmitLoop();
      }

      console.log(`ControlModule: 间隔速率设置为: ${rate}x，发射速率重置为: 1x`);

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '设置间隔速率';

      return { success: true, rate: rate };
    } catch (error) {
      console.error('ControlModule: 设置间隔速率失败:', error);
      this.performance.errorCount++;
      this.handleError(error, '速率控制');
      return { success: false, error: error.message };
    }
  }

  setEmitMode(mode) {
    try {
      this.controlState.isLoopMode = (mode === 'loop');

      // 更新当前任务的模式
      if (this.currentEmitTask) {
        this.currentEmitTask.isLoopMode = this.controlState.isLoopMode;
      }

      console.log(`ControlModule: 发射模式设置为: ${mode}`);

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '设置发射模式';

      return { success: true, mode: mode, isLoopMode: this.controlState.isLoopMode };
    } catch (error) {
      console.error('ControlModule: 设置发射模式失败:', error);
      this.performance.errorCount++;
      this.handleError(error, '模式控制');
      return { success: false, error: error.message };
    }
  }

  /**
   * 学习控制方法已移除 - 功能在信号管理模块中
   */

  /**
   * 获取全部信号用于发射
   */
  async getAllSignalsForEmit() {


    return new Promise((resolve) => {
      // 设置超时，避免无限等待
      const timeout = setTimeout(() => {
        console.error('❌ ControlModule: 获取信号超时，信号管理模块可能未响应');
        resolve([]);
      }, 2000);

      this.emitEvent('signal.request.all', {
        source: 'ControlModule',
        purpose: 'emit-all',
        callback: (signals) => {
          clearTimeout(timeout);
          console.log(`✅ ControlModule: 收到信号响应，数量: ${signals?.length || 0}`);



          resolve(signals || []);
        }
      });
    });
  }

  /**
   * 处理批量发射请求（来自信号管理模块）
   */
  async handleBatchEmitRequest(data) {
    try {
      const { signals, source } = data;

      if (!signals || signals.length === 0) {
        this.handleError(new Error('没有选中的信号'), '批量发射');
        return;
      }

      // 模拟日志已移除

      // 创建选中信号发射任务
      const task = this.createEmitTask(signals, 'batch', { source: source });

      // 启动任务
      const result = await this.startEmitTask(task);

      // 回复信号管理模块
      this.emitEvent('signal.batch.emit.response', {
        success: result.success,
        taskId: result.taskId,
        queued: result.queued || false
      });

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '批量发射请求';

    } catch (error) {
      this.performance.errorCount++;
      this.handleError(error, '批量发射');

      // 回复错误
      this.emitEvent('signal.batch.emit.response', {
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 追加信号到当前任务队列
   */
  appendSignalsToCurrentTask(newSignals) {
    if (!this.currentEmitTask || !this.currentEmitTask.signals) {
      console.warn('ControlModule: 无法追加信号 - 当前任务无效');
      return;
    }

    // 过滤重复信号
    const currentSignalIds = this.currentEmitTask.signals.map(s => s.id);
    const uniqueNewSignals = newSignals.filter(signal =>
      !currentSignalIds.includes(signal.id)
    );

    if (uniqueNewSignals.length === 0) {

      return;
    }

    // 追加信号到队列末尾
    this.currentEmitTask.signals.push(...uniqueNewSignals);



    // 发布追加事件 - 通知其他模块更新UI
    this.emitEvent('control.signals.appended', {
      taskId: this.currentEmitTask.id,
      appendedSignals: uniqueNewSignals,
      totalSignals: this.currentEmitTask.signals.length,
      currentIndex: this.currentEmitTask.currentSignalIndex || 0
    });
  }

  async startEmitLoop() {
    try {
      if (!this.currentEmitTask || this.currentEmitTask.status !== 'running') {
        console.warn('ControlModule: 没有运行中的任务');
        return;
      }

      const task = this.currentEmitTask;

      // 检查定时任务是否到达结束时间
      const isExpired = await this.isTimerTaskExpired(task);
      if (isExpired) {
        console.log('ControlModule: 定时任务已到达结束时间，完成任务');
        this.completeCurrentTask();
        return;
      }

      // 检查是否有信号需要发射
      if (!task.signals || task.signals.length === 0) {
        console.warn('ControlModule: 没有信号可发射');
        this.completeCurrentTask();
        return;
      }

      // 获取当前信号
      const currentSignal = task.signals[task.currentSignalIndex];
      if (!currentSignal) {
        console.error('ControlModule: 当前信号不存在');
        this.completeCurrentTask();
        return;
      }

      // 根据速率控制执行发射
      this.executeEmitWithRateControl(currentSignal, task);

    } catch (error) {
      console.error('ControlModule: 发射循环失败:', error);
      this.completeCurrentTask();
    }
  }

  /**
   * 根据速率控制执行发射
   */
  async executeEmitWithRateControl(signal, task) {
    try {
      if (this.controlState.intervalRate > 1) {
        // 用户设置了间隔速率 → 按间隔速率执行
        // 重复发射当前信号N次，然后切换到下一个信号
        await this.executeIntervalRateEmit(signal, task);
      } else {
        // 默认或用户设置了发射速率 → 按发射速率执行
        // 快速切换不同信号（发射速率>1时）或正常发射（发射速率=1时）
        await this.executeEmitRateEmit(signal, task);
      }
    } catch (error) {
      console.error('ControlModule: 速率控制发射失败:', error);
      // 发射失败时继续下一个信号
      this.moveToNextSignal(task);
    }
  }

  /**
   * 间隔速率发射：用户设置了间隔速率>1x
   * 在1秒内重复发射当前信号N次，然后切换到下一个信号
   */
  async executeIntervalRateEmit(signal, task) {
    const repeatCount = this.controlState.intervalRate;
    const intervalDelay = 1000 / repeatCount; // 在1秒内平均分配

    // 模拟日志已移除

    for (let i = 0; i < repeatCount; i++) {
      // 检查任务是否仍在运行
      if (!this.currentEmitTask || this.currentEmitTask.status !== 'running') {
        return;
      }

      // 发射信号
      await this.emitSingleSignal(signal);
      task.emitCount++;

      // 发布进度事件
      this.emitProgressEvent(signal, task);

      // 等待间隔（除了最后一次）
      if (i < repeatCount - 1) {
        await this.delay(intervalDelay);
      }
    }

    // ✅ 简化：重复发射完成，更新索引并发布进度事件
    task.currentSignalIndex++;

    // 发布进度事件 - 显示当前信号发射完成的进度
    this.emitProgressEvent(signal, task);

    // 检查是否完成一轮并处理下一个信号
    this.handleSignalCompletion(task);
  }

  /**
   * 发射速率发射：默认模式或用户设置了发射速率>1x
   * 发射当前信号1次，然后根据发射速率决定切换延迟
   */
  async executeEmitRateEmit(signal, task) {
    // 模拟日志已移除

    // 发射当前信号

    await this.emitSingleSignal(signal);
    task.emitCount++;

    // ✅ 简化：发射完成后直接更新索引并发布进度事件
    task.currentSignalIndex++;

    // 发布进度事件 - 显示当前信号发射完成的进度
    this.emitProgressEvent(signal, task);

    // 检查是否完成一轮并处理下一个信号
    this.handleSignalCompletion(task);
  }

  /**
   * 处理信号完成 - 新的统一处理方法
   */
  handleSignalCompletion(task) {
    // 检查是否完成一轮（currentSignalIndex已经在executeEmitRateEmit中递增）
    if (task.currentSignalIndex >= task.signals.length) {
      if (task.isLoopMode) {
        // 循环模式 - 重新开始
        task.currentSignalIndex = 0;
        // 模拟日志已移除
      } else {
        // 单次模式 - 完成任务
        // 模拟日志已移除
        this.completeCurrentTask();
        return;
      }
    }

    // 计算下次发射延迟
    const delay = this.calculateNextEmitDelay();

    // 设置下次发射
    this.emitTimeout = setTimeout(() => {
      this.startEmitLoop();
    }, delay);
  }

  /**
   * 移动到下一个信号 - 保留用于间隔速率模式
   */
  moveToNextSignal(task) {
    task.currentSignalIndex++;

    // 检查是否完成一轮
    if (task.currentSignalIndex >= task.signals.length) {
      if (task.isLoopMode) {
        // 循环模式 - 重新开始
        task.currentSignalIndex = 0;
        // 模拟日志已移除
      } else {
        // 单次模式 - 完成任务
        // 模拟日志已移除
        this.completeCurrentTask();
        return;
      }
    }

    // 计算下次发射延迟
    const delay = this.calculateNextEmitDelay();

    // 设置下次发射
    this.emitTimeout = setTimeout(() => {
      this.startEmitLoop();
    }, delay);
  }

  async emitSingleSignal(signal) {
    try {
      // 模拟日志已移除

      // 发布信号发射开始事件
      this.emitEvent('control.signal.emitting', {
        signal: signal,
        timestamp: Date.now()
      });

      // 模拟或真实信号发射
      const result = await this.performSignalEmit(signal);

      // 发布信号发射完成事件
      this.emitEvent('control.signal.emitted', {
        signal: signal,
        result: result,
        timestamp: Date.now()
      });

      // ✅ 统一发射逻辑：通知信号管理模块更新统计
      // 控制模块负责发射，信号管理模块负责统计管理
      this.emitEvent('signal.emit.success', {
        signalId: signal.id,
        signalName: signal.name,
        timestamp: Date.now(),
        source: 'ControlModule'
      });

      return result;

    } catch (error) {
      console.error(`ControlModule: 信号发射失败: ${signal.name}`, error);

      // 发布信号发射失败事件
      this.emitEvent('control.signal.emit.failed', {
        signal: signal,
        error: error.message,
        timestamp: Date.now()
      });

      // 通知信号管理模块发射失败
      this.emitEvent('signal.emit.failed', {
        signalId: signal.id,
        signalName: signal.name,
        error: error.message,
        timestamp: Date.now(),
        source: 'ControlModule'
      });

      throw error;
    }
  }

  /**
   * 执行信号发射 - 通过后端API
   */
  async performSignalEmit(signal) {
    return await this.realSignalEmit(signal);
  }

  /**
   * 真实信号发射（后期实现）
   */
  async realSignalEmit(signal) {
    try {
      const response = await this.requestESP32('/api/emit/signal', {
        method: 'POST',
        body: JSON.stringify({
          signalCode: signal.signalCode,
          frequency: signal.frequency,
          protocol: signal.protocol
        })
      });

      return response;
    } catch (error) {
      throw new Error(`ESP32发射失败: ${error.message}`);
    }
  }

  /**
   * 计算下次发射延迟 - 应由后端控制
   */
  calculateNextEmitDelay() {
    // 发射延迟应由后端API控制，不在前端硬编码
    console.warn('⚠️ 发射延迟计算应由后端API控制');
    return 0; // 不设置默认延迟
  }

  /**
   * 发布进度事件 - 简化版本
   * @param {Object} signal - 当前信号
   * @param {Object} task - 当前任务
   */
  emitProgressEvent(signal, task) {
    const progressData = {
      task: {
        id: task.id,
        type: task.type,
        name: task.name,
        currentSignalIndex: task.currentSignalIndex,
        totalSignals: task.signals.length,
        emitCount: task.emitCount,
        isLoopMode: task.isLoopMode  // ✅ 添加循环模式标记
      },
      currentSignal: signal,
      timestamp: Date.now()
    };



    this.emitEvent('control.emit.progress', progressData);
  }

  /**
   * 完成当前任务
   */
  async completeCurrentTask() {
    try {
      const completedTask = this.currentEmitTask;
      if (!completedTask) {
        this.debugTracker.log('TASK_OPERATION', '尝试完成任务但没有当前任务', {
          currentState: this.getDebugState()
        });
        return;
      }

      this.debugTracker.log('TASK_OPERATION', '开始完成当前任务', {
        completedTask: {
          id: completedTask.id,
          name: completedTask.name,
          type: completedTask.type,
          priority: completedTask.priority,
          currentSignalIndex: completedTask.currentSignalIndex,
          totalSignals: completedTask.signals?.length,
          emitCount: completedTask.emitCount,
          isLoopMode: completedTask.isLoopMode,
          status: completedTask.status
        },
        beforeState: this.getDebugState()
      });

      // 更新任务状态
      completedTask.status = 'completed';
      completedTask.endTime = Date.now();
      completedTask.duration = completedTask.endTime - completedTask.startTime;

      // 清理状态
      this.controlState.isTransmitting = false;
      this.controlState.isPaused = false;
      this.controlState.currentTaskId = '';

      this.debugTracker.log('STATE_CHANGE', '任务完成后状态清理', {
        afterState: this.getDebugState()
      });

      // 清理定时器
      if (this.emitTimeout) {
        clearTimeout(this.emitTimeout);
        this.emitTimeout = null;
      }

      // 添加到历史记录
      this.addTaskToHistory(completedTask);

      // 更新UI
      this.updateControlUI();

      // 发布任务完成事件 - 确保数据结构完整
      this.emitEvent('control.emit.completed', {
        task: {
          id: completedTask.id,
          name: completedTask.name,
          type: completedTask.type,
          signals: completedTask.signals,
          emitCount: completedTask.emitCount,
          startTime: completedTask.startTime,
          endTime: completedTask.endTime,
          duration: completedTask.duration,
          isLoopMode: completedTask.isLoopMode  // ✅ 添加循环模式标记
        },
        duration: completedTask.duration
      });

      // 如果是定时任务，通知定时模块
      if (completedTask.timerTaskId) {
        this.emitEvent('timer.task.execution.completed', {
          taskId: completedTask.timerTaskId,
          executionTime: completedTask.endTime,
          success: true,
          duration: completedTask.duration
        });
        console.log('ControlModule: 已通知定时模块任务完成:', completedTask.timerTaskId);
      }

      // 通知其他模块重置（单次模式）
      if (!completedTask.isLoopMode) {
        this.notifyModulesReset();
      }

      // 记录日志
      // 模拟日志已移除

      // 清空当前任务
      this.currentEmitTask = null;

      // 处理下一个任务
      await this.processNextTask();

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '任务完成';

    } catch (error) {
      this.performance.errorCount++;
      console.error('ControlModule: 完成任务失败:', error);
    }
  }

  /**
   * 处理下一个任务
   */
  async processNextTask() {
    // 优先恢复暂停的任务
    const resumed = await this.resumePausedTasks();
    if (resumed) return;

    // 处理队列中的任务
    const processed = await this.processNextQueuedTask();
    if (processed) return;

    // 没有更多任务，完全停止
    console.log('ControlModule: 所有任务已完成');
  }

  /**
   * 通知其他模块重置
   */
  notifyModulesReset() {
    this.emitEvent('system.task.completed', {
      source: 'ControlModule',
      action: 'reset_required',
      timestamp: Date.now()
    });

    // 特别通知显示模块重置
    this.emitEvent('display.reset.request', {
      source: 'ControlModule',
      reason: '任务完成'
    });
  }

  /**
   * 检查定时任务是否已过期（到达结束时间）
   */
  async isTimerTaskExpired(task) {
    // 只检查定时任务
    if (!task.timerTaskId || task.type !== 'timer') {
      return false;
    }

    // 通过事件系统获取定时任务信息
    return new Promise((resolve) => {
      this.emitEvent('timer.task.info.request', {
        taskId: task.timerTaskId,
        callback: (timerTask) => {
          if (!timerTask || !timerTask.endTime) {
            resolve(false);
            return;
          }

          // 解析结束时间
          const now = new Date();
          const [endHour, endMinute, endSecond = 0] = timerTask.endTime.split(':').map(Number);

          // 创建今天的结束时间
          const endTime = new Date();
          endTime.setHours(endHour, endMinute, endSecond, 0);

          // 如果结束时间已过，任务应该停止
          const isExpired = now >= endTime;

          if (isExpired) {
            console.log(`ControlModule: 定时任务 ${task.name} 已到达结束时间 ${timerTask.endTime}`);
          }

          resolve(isExpired);
        }
      });
    });
  }

  /**
   * 获取调试状态
   */
  getDebugState() {
    return {
      controlState: { ...this.controlState },
      currentEmitTask: this.currentEmitTask ? {
        id: this.currentEmitTask.id,
        name: this.currentEmitTask.name,
        type: this.currentEmitTask.type,
        status: this.currentEmitTask.status,
        currentSignalIndex: this.currentEmitTask.currentSignalIndex,
        totalSignals: this.currentEmitTask.signals?.length,
        isLoopMode: this.currentEmitTask.isLoopMode,
        emitCount: this.currentEmitTask.emitCount
      } : null,
      taskQueueLength: this.taskQueue.length,
      pausedTasksLength: this.pausedTasks.length
    };
  }

  /**
   * 获取完整调试报告
   */
  getFullDebugReport() {
    return this.debugTracker.getReport();
  }

  /**
   * 销毁模块 - 清理所有资源
   */
  destroy() {
    try {
      // 停止所有任务
      this.stopEmit();

      // 清理定时器
      if (this.emitTimeout) {
        clearTimeout(this.emitTimeout);
        this.emitTimeout = null;
      }

      // 清理事件监听器
      this.eventBus.off('control.pause.request');
      this.eventBus.off('control.resume.request');
      this.eventBus.off('control.request.signals');
      this.eventBus.off('control.request.selected-signals');
      this.eventBus.off('control.request.send-signal');
      this.eventBus.off('control.paused-tasks.status.request');
      this.eventBus.off('control.task.status.request');
      this.eventBus.off('control.debug.report.request');
      this.eventBus.off('control.debug.state.request');

      // 清理数据
      this.taskQueue.clear();
      this.pausedTasks.clear();
      this.taskHistory = [];
      this.currentEmitTask = null;

      console.log('ControlModule: 模块已销毁');
    } catch (error) {
      console.error('ControlModule: 销毁模块失败:', error);
    }
  }

  /**
   * 延迟工具方法 - 使用统一定时器管理器
   */
  delay(ms) {
    return new Promise(resolve => {
      const timerId = `delay_${Date.now()}_${Date.now().toString(36)}`;
      window.UnifiedTimerManager.addTimer(timerId, resolve, ms, false);
    });
  }

  restartEmitLoop() {
    try {
      // 停止当前循环
      if (this.emitTimeout) {
        clearTimeout(this.emitTimeout);
        this.emitTimeout = null;
      }

      // 重新开始循环
      this.startEmitLoop();

      console.log('ControlModule: 发射循环已重启');
    } catch (error) {
      console.error('ControlModule: 重启发射循环失败:', error);
    }
  }

  /**
   * UI更新方法
   */
  updateControlUI() {
    try {
      // 更新发射控制按钮状态
      if (this.controlState.isTransmitting) {
        if (this.controlState.isPaused) {
          // 暂停状态
          this.setButtonState('startEmitBtn', true);
          this.setButtonState('pauseEmitBtn', true);
          this.setButtonState('resumeEmitBtn', false);
          this.setButtonState('stopEmitBtn', false);
        } else {
          // 发射中状态
          this.setButtonState('startEmitBtn', true);
          this.setButtonState('pauseEmitBtn', false);
          this.setButtonState('resumeEmitBtn', true);
          this.setButtonState('stopEmitBtn', false);
        }
      } else {
        // 待机状态
        this.setButtonState('startEmitBtn', false);
        this.setButtonState('pauseEmitBtn', true);
        this.setButtonState('resumeEmitBtn', true);
        this.setButtonState('stopEmitBtn', true);
      }

    } catch (error) {
      console.error('ControlModule: 更新控制UI失败:', error);
    }
  }



  setButtonState(buttonId, disabled) {
    const button = this.elements[buttonId];
    if (button) {
      button.disabled = disabled;
    }
  }

  /**
   * 显示任务历史
   */
  showTaskHistory() {
    try {
      const history = this.getTaskHistory();

      const modalContent = `
        <div class="task-history-dialog">
          <h3>任务执行历史</h3>
          <div class="history-content">
            ${history.length > 0 ? this.renderTaskHistoryList(history) : '<p class="no-history">暂无执行历史</p>'}
          </div>
          <div class="history-actions">
            <button class="btn secondary" data-action="clear-history">清空历史</button>
            <button class="btn primary" data-action="close-modal">关闭</button>
          </div>
        </div>
      `;

      // 通过事件系统显示模态框
      this.emitEvent('system.modal.show', {
        content: modalContent,
        title: '任务执行历史'
      });

      // 绑定清空历史事件
      const clearBtn = $('[data-action="clear-history"]');
      if (clearBtn) {
        clearBtn.addEventListener('click', () => {
          this.clearTaskHistory();
          // 通过事件系统隐藏模态框
          this.emitEvent('system.modal.hide');
          this.showTaskHistory(); // 重新显示
        });
      }

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '查看任务历史';

    } catch (error) {
      this.performance.errorCount++;
      this.handleError(error, '任务历史');
    }
  }

  /**
   * 渲染任务历史列表
   */
  renderTaskHistoryList(history) {
    return `
      <div class="history-list">
        ${history.map(task => `
          <div class="history-item">
            <div class="task-info">
              <h4>${task.name || '未命名任务'}</h4>
              <p class="task-type">${this.getTaskTypeName(task.type)}</p>
              <p class="task-time">${new Date(task.startTime).toLocaleString()}</p>
            </div>
            <div class="task-status">
              <span class="status-badge ${task.status}">${this.getStatusText(task.status)}</span>
              ${task.duration ? `<span class="duration">${Math.round(task.duration/1000)}秒</span>` : ''}
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  /**
   * 加载任务历史
   */
  loadTaskHistory() {
    try {
      const stored = R1Utils.storage.get('controlModule_taskHistory');
      if (stored && Array.isArray(stored)) {
        this.taskHistory = stored;
        console.log(`✅ ControlModule: 加载了 ${this.taskHistory.length} 条任务历史`);
      } else {
        this.taskHistory = [];
      }
    } catch (error) {
      console.error('❌ ControlModule: 加载任务历史失败:', error);
      this.taskHistory = [];
    }
  }

  /**
   * 保存任务历史
   */
  saveTaskHistory() {
    try {
      // 只保留最近100条记录
      const historyToSave = this.taskHistory.slice(-100);
      R1Utils.storage.set('controlModule_taskHistory', historyToSave);
      console.log(`✅ ControlModule: 保存了 ${historyToSave.length} 条任务历史`);
    } catch (error) {
      console.error('❌ ControlModule: 保存任务历史失败:', error);
    }
  }

  /**
   * 添加任务到历史
   */
  addTaskToHistory(task) {
    try {
      const historyItem = {
        id: task.id,
        name: task.name || `任务_${new Date().toLocaleTimeString()}`,
        type: task.type || 'emit',
        status: task.status,
        startTime: task.startTime,
        endTime: task.endTime,
        duration: task.duration,
        signalCount: task.signals?.length || 0,
        emitCount: task.emitCount || 0
      };

      this.taskHistory.push(historyItem);
      this.saveTaskHistory();

      console.log(`✅ ControlModule: 任务已添加到历史: ${historyItem.name}`);
    } catch (error) {
      console.error('❌ ControlModule: 添加任务历史失败:', error);
    }
  }

  /**
   * 获取任务历史
   */
  getTaskHistory() {
    return this.taskHistory.slice().reverse(); // 返回副本，最新的在前
  }

  /**
   * 清空任务历史
   */
  clearTaskHistory() {
    try {
      this.taskHistory = [];
      this.saveTaskHistory();

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '清空任务历史';

      this.handleSuccess('任务历史已清空', '任务历史');
    } catch (error) {
      this.performance.errorCount++;
      this.handleError(error, '清空历史');
    }
  }

  /**
   * 工具方法
   */
  getTaskTypeName(type) {
    const types = {
      'emit': '信号发射',
      'single': '单次发射',
      'batch': '批量发射',
      'sequence': '序列发射',
      'loop': '循环发射'
    };
    return types[type] || '未知类型';
  }

  getStatusText(status) {
    const statuses = {
      'completed': '已完成',
      'stopped': '已停止',
      'failed': '失败',
      'paused': '已暂停',
      'running': '运行中'
    };
    return statuses[status] || status;
  }
}

// 导出控制模块
window.ControlModule = ControlModule;

// 添加全局调试工具 - 通过事件系统访问
window.R1Debug = window.R1Debug || {};
window.R1Debug.getControlDebugReport = () => {
  // 通过事件系统获取调试报告 - 符合架构标准
  let debugReport = { error: '控制模块未找到或调试功能不可用' };

  if (window.R1System?.eventBus) {
    window.R1System.eventBus.emit('control.debug.report.request', {
      callback: (report) => {
        if (report) {
          debugReport = report;
        }
      }
    });
  }

  return debugReport;
};

window.R1Debug.logControlState = () => {
  // 通过事件系统获取状态 - 符合架构标准
  if (window.R1System?.eventBus) {
    window.R1System.eventBus.emit('control.debug.state.request', {
      callback: (state) => {
        if (state) {
          console.log('🔍 当前控制模块状态:', state);
        } else {
          console.log('❌ 控制模块未找到');
        }
      }
    });
  } else {
    console.log('❌ 事件系统未找到');
  }
};

window.R1Debug.downloadDebugReport = () => {
  const report = window.R1Debug.getControlDebugReport();
  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `control-debug-report-${Date.now()}.json`;
  a.click();
  URL.revokeObjectURL(url);
};

/**
 * @file ConfigService.h
 * @brief 配置管理服务 - 系统配置管理
 * 
 * 基于前端完整数据文档的配置管理需求
 * 支持配置读取、更新、验证、持久化
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef CONFIG_SERVICE_H
#define CONFIG_SERVICE_H

#include "BaseService.h"
#include "../config/SystemConfig.h"

class ConfigService : public BaseService {
private:
    SystemConfig* systemConfig;
    bool configChanged;
    uint32_t lastSaveTime;
    uint32_t autoSaveInterval;

public:
    ConfigService(EventManager* em);
    virtual ~ConfigService() = default;

    // BaseService接口实现
    bool init() override;
    void loop() override;
    void cleanup() override;

    // 配置访问接口
    const NetworkConfig& getNetworkConfig();
    const HardwareConfig& getHardwareConfig();
    const SystemSettings& getSystemSettings();
    const ServerConfig& getServerConfig();

    // 配置更新接口
    bool updateNetworkConfig(const NetworkConfig& config);
    bool updateHardwareConfig(const HardwareConfig& config);
    bool updateSystemSettings(const SystemSettings& settings);
    bool updateServerConfig(const ServerConfig& config);

    // JSON配置接口
    JsonDocument getConfigJson();
    bool updateConfigFromJson(const JsonObject& json);

    // 配置管理
    bool saveConfig();
    bool loadConfig();
    bool resetToDefaults();
    bool validateConfig();

    // 状态查询
    bool hasUnsavedChanges() const { return configChanged; }
    uint32_t getLastSaveTime() const { return lastSaveTime; }

private:
    void markConfigChanged();
    void onConfigSaved();
    void broadcastConfigChanged(const std::string& section);
};

#endif

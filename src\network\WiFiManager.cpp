/**
 * @file WiFiManager.cpp
 * @brief WiFi管理器实现占位符
 */

#include "WiFiManager.h"

WiFiManager::WiFiManager(EventManager* em) 
    : BaseService(em, "WiFiManager", ServiceType::CORE0_SERVICE),
      currentState(WiFiState::DISCONNECTED) {
}

bool WiFiManager::init() {
    logInfo("初始化WiFi管理器...");
    setInitialized(true);
    return true;
}

void WiFiManager::loop() {
    updateLoopStats();
}

void WiFiManager::cleanup() {
    logInfo("清理WiFi管理器...");
}

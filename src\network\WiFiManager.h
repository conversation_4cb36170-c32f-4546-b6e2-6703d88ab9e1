/**
 * @file WiFiManager.h
 * @brief WiFi连接管理器 - Core 0网络服务
 * 
 * 基于前端完整数据文档的网络连接需求
 * 支持AP模式、Station模式、自动重连
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include "../services/BaseService.h"
#include "../config/SystemConfig.h"
#include <WiFi.h>

// WiFi状态枚举
enum class WiFiState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    AP_MODE,
    ERROR
};

class WiFiManager : public BaseService {
private:
    WiFiState currentState;
    NetworkConfig networkConfig;
    
    // 连接管理
    bool apModeEnabled;
    bool stationModeEnabled;
    uint32_t lastConnectionAttempt;
    uint32_t connectionTimeout;
    uint8_t connectionRetries;
    uint8_t maxRetries;
    
    // 状态监控
    int32_t lastRSSI;
    uint32_t connectionStartTime;
    uint32_t totalConnections;
    uint32_t totalDisconnections;

public:
    WiFiManager(EventManager* em);
    virtual ~WiFiManager() = default;

    // BaseService接口实现
    bool init() override;
    void loop() override;
    void cleanup() override;

    // WiFi控制接口
    bool startAP();
    bool stopAP();
    bool connectToWiFi(const std::string& ssid, const std::string& password);
    bool disconnect();
    
    // 状态查询
    WiFiState getState() const { return currentState; }
    bool isConnected() const { return currentState == WiFiState::CONNECTED; }
    bool isAPMode() const { return currentState == WiFiState::AP_MODE; }
    
    // 网络信息
    std::string getLocalIP();
    std::string getMACAddress();
    int32_t getRSSI();
    std::string getSSID();
    
    // 统计信息
    uint32_t getUptime();
    uint32_t getTotalConnections() const { return totalConnections; }
    uint32_t getTotalDisconnections() const { return totalDisconnections; }
    
    // 配置更新
    void updateConfig(const NetworkConfig& config);

private:
    // 状态管理
    void setState(WiFiState state);
    void handleStateChange(WiFiState oldState, WiFiState newState);
    
    // 连接处理
    void processConnection();
    void handleConnectionSuccess();
    void handleConnectionFailure();
    void attemptReconnection();
    
    // AP模式处理
    bool setupAPMode();
    void handleAPClients();
    
    // 事件处理
    void onWiFiEvent(WiFiEvent_t event);
    static void wifiEventHandler(WiFiEvent_t event);
    
    // 网络配置
    bool configureStaticIP();
    bool validateNetworkConfig();
    
    // WebSocket事件广播
    void broadcastWiFiConnected();
    void broadcastWiFiDisconnected(const std::string& reason);
    void broadcastNetworkStatus();
};

#endif

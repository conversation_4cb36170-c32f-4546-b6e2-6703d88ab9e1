/**
 * @file IRReceiver.h
 * @brief 红外接收器 - Core 1硬件控制
 * 
 * 基于前端完整数据文档的信号学习需求
 * 支持多种红外协议识别、信号质量评估
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef IR_RECEIVER_H
#define IR_RECEIVER_H

#include <Arduino.h>
#include <IRremoteESP8266.h>
#include <IRrecv.h>
#include <IRutils.h>
#include "../types/SignalData.h"
#include "../core/EventManager.h"
#include <vector>
#include <string>

// 学习状态
enum class LearningState {
    IDLE,
    WAITING,
    SIGNAL_DETECTED,
    ANALYZING,
    COMPLETED,
    TIMEOUT,
    ERROR
};

// 学习会话
struct LearningSession {
    bool active;
    LearningState state;
    uint32_t startTime;
    uint32_t timeout;
    std::string signalName;
    std::string signalType;
    uint8_t attempts;
    std::vector<decode_results> capturedSignals;
    
    LearningSession() : active(false), state(LearningState::IDLE), 
                       startTime(0), timeout(30000), attempts(0) {}
};

class IRReceiver {
private:
    IRrecv* irRecv;
    uint8_t receivePin;
    uint16_t bufferSize;
    EventManager* eventManager;
    
    // 学习状态
    LearningSession learningSession;
    decode_results lastResult;
    
    // 信号分析
    uint8_t signalQuality;
    uint32_t signalStrength;
    std::string detectedProtocol;
    
    // 统计信息
    uint32_t totalSignalsReceived;
    uint32_t validSignalsReceived;
    uint32_t learningAttempts;
    uint32_t successfulLearning;

public:
    IRReceiver(EventManager* em);
    ~IRReceiver();

    // 生命周期
    bool init(uint8_t pin, uint16_t bufferSize = 1024);
    void cleanup();
    void loop();

    // 学习控制接口 (匹配前端API需求)
    bool startLearning(const std::string& signalName, const std::string& signalType, 
                      uint32_t timeout = 30000);
    void stopLearning();
    LearningState getLearningState() const { return learningSession.state; }
    bool isLearning() const { return learningSession.active; }
    
    // 学习结果
    SignalData getLearnedSignal();
    uint8_t getSignalQuality() const { return signalQuality; }
    std::string getDetectedProtocol() const { return detectedProtocol; }
    
    // 配置管理
    bool setReceivePin(uint8_t pin);
    uint8_t getReceivePin() const { return receivePin; }
    bool setBufferSize(uint16_t size);
    uint16_t getBufferSize() const { return bufferSize; }
    
    // 统计信息
    uint32_t getTotalSignalsReceived() const { return totalSignalsReceived; }
    uint32_t getValidSignalsReceived() const { return validSignalsReceived; }
    uint32_t getLearningAttempts() const { return learningAttempts; }
    uint32_t getSuccessfulLearning() const { return successfulLearning; }
    float getLearningSuccessRate() const;
    
    // 硬件测试
    bool testReceiver();
    bool isHardwareReady();

private:
    // 学习处理
    void processLearning();
    void handleSignalDetected();
    void analyzeSignal(const decode_results& result);
    void completeLearning(bool success);
    void handleLearningTimeout();
    void handleLearningError(const std::string& error);
    
    // 信号处理
    bool isValidSignal(const decode_results& result);
    SignalData convertToSignalData(const decode_results& result);
    uint8_t calculateSignalQuality(const decode_results& result);
    std::string identifyProtocol(const decode_results& result);
    
    // 数据转换
    std::string uint64ToHexString(uint64_t value);
    std::string rawDataToString(const uint16_t* rawData, uint16_t length);
    std::string decodeTypeToString(decode_type_t type);
    
    // 状态管理
    void setLearningState(LearningState state);
    void resetLearningSession();
    void updateStatistics();
    
    // 事件发布 (匹配前端WebSocket事件)
    void emitLearningStarted();
    void emitSignalDetected(const SignalData& signal);
    void emitLearningCompleted(const SignalData& signal, uint8_t quality);
    void emitLearningFailed(const std::string& error);
    void emitLearningTimeout();
    
    // 硬件控制
    bool initializeHardware();
    void enableReceiver();
    void disableReceiver();
    void resumeReceiver();
    
    // 信号过滤和验证
    bool filterNoise(const decode_results& result);
    bool validateSignalLength(const decode_results& result);
    bool validateSignalTiming(const decode_results& result);
    bool isDuplicateSignal(const decode_results& result);
    
    // 错误处理
    void logReceiveError(const std::string& operation, const std::string& error);
    void handleHardwareError(const std::string& error);
    
    // 调试和诊断
    void printSignalInfo(const decode_results& result);
    void dumpSignalData(const decode_results& result);
};

#endif

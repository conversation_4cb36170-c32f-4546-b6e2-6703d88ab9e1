/**
 * @file SignalService.h
 * @brief 信号管理服务 - 红外信号CRUD操作
 * 
 * 基于前端完整数据文档的信号管理需求
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef SIGNAL_SERVICE_H
#define SIGNAL_SERVICE_H

#include "BaseService.h"
#include "../types/SignalData.h"
#include <vector>

class SignalService : public BaseService {
private:
    std::vector<SignalData> signals;

public:
    SignalService(EventManager* em);
    virtual ~SignalService() = default;

    // BaseService接口实现
    bool init() override;
    void loop() override;
    void cleanup() override;

    // 信号管理接口
    bool addSignal(const SignalData& signal);
    SignalData getSignal(const std::string& id);
    std::vector<SignalData> getAllSignals();
    bool updateSignal(const std::string& id, const SignalData& signal);
    bool deleteSignal(const std::string& id);
    bool clearAllSignals();
};

#endif

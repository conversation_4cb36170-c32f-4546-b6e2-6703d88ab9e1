/**
 * @file EventTypes.h
 * @brief 事件类型定义 - 双核架构事件系统
 * 
 * 基于前端完整数据文档的事件需求设计
 * 支持双核间通信和前端WebSocket事件
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef EVENT_TYPES_H
#define EVENT_TYPES_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <functional>
#include <string>

// 事件类型枚举
enum class EventType {
    // 系统事件
    SYSTEM_READY,
    SYSTEM_ERROR,
    SYSTEM_RESTART,
    SYSTEM_SHUTDOWN,

    // 网络事件 (Core 0)
    WIFI_CONNECTED,
    WIFI_DISCONNECTED,
    WEBSOCKET_CONNECTED,
    WEBSOCKET_DISCONNECTED,
    HTTP_REQUEST_RECEIVED,

    // 信号事件 (Core 1 -> Core 0)
    SIGNAL_LEARNED,
    SIGNAL_SENT,
    SIGNAL_ADDED,
    SIGNAL_DELETED,
    SIGNAL_UPDATED,
    SIGNAL_LEARNING_TIMEOUT,

    // 控制事件 (Core 0 -> Core 1)
    LEARNING_START_REQUEST,
    LEARNING_STOP_REQUEST,
    EMIT_SIGNAL_REQUEST,
    EMIT_BATCH_REQUEST,

    // 硬件事件 (Core 1)
    LEARNING_STARTED,
    LEARNING_STOPPED,
    EMIT_STARTED,
    EMIT_COMPLETED,
    HARDWARE_ERROR,

    // 状态事件 (双向)
    STATUS_UPDATE,
    ERROR_OCCURRED,
    HEARTBEAT,

    // 双核通信事件
    CORE_SYNC_REQUEST,
    CORE_SYNC_RESPONSE,
    INTER_CORE_MESSAGE
};

// 事件优先级
enum class EventPriority {
    LOW = 1,
    NORMAL = 2,
    HIGH = 3,
    CRITICAL = 4
};

// 事件数据结构
struct EventData {
    EventType type;
    EventPriority priority;
    JsonDocument data;
    uint64_t timestamp;
    uint8_t sourceCore;         // 0 = Core 0, 1 = Core 1
    uint8_t targetCore;         // 0 = Core 0, 1 = Core 1, 255 = 广播
    std::string eventId;        // 事件唯一ID

    EventData() : priority(EventPriority::NORMAL), timestamp(millis()), 
                  sourceCore(0), targetCore(255) {}

    EventData(EventType t, const JsonDocument& d = JsonDocument()) 
        : type(t), priority(EventPriority::NORMAL), data(d), 
          timestamp(millis()), sourceCore(0), targetCore(255) {}

    // 生成事件ID
    void generateId() {
        eventId = "evt_" + std::to_string(timestamp) + "_" + std::to_string(random(1000, 9999));
    }

    // 检查是否过期
    bool isExpired(uint32_t timeoutMs = 30000) const {
        return (millis() - timestamp) > timeoutMs;
    }

    // 设置目标核心
    void setTargetCore(uint8_t core) {
        targetCore = core;
    }

    // 设置源核心
    void setSourceCore(uint8_t core) {
        sourceCore = core;
    }

    // 设置优先级
    void setPriority(EventPriority p) {
        priority = p;
    }

    // 转换为JSON
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["type"] = static_cast<int>(type);
        doc["priority"] = static_cast<int>(priority);
        doc["timestamp"] = timestamp;
        doc["sourceCore"] = sourceCore;
        doc["targetCore"] = targetCore;
        doc["eventId"] = eventId;
        doc["data"] = data;
        return doc;
    }

    // 从JSON创建
    static EventData fromJson(const JsonObject& json) {
        EventData event;
        event.type = static_cast<EventType>(json["type"].as<int>());
        event.priority = static_cast<EventPriority>(json["priority"].as<int>());
        event.timestamp = json["timestamp"].as<uint64_t>();
        event.sourceCore = json["sourceCore"].as<uint8_t>();
        event.targetCore = json["targetCore"].as<uint8_t>();
        event.eventId = json["eventId"].as<std::string>();
        event.data = json["data"];
        return event;
    }
};

// 事件处理器类型
using EventHandler = std::function<void(const EventData&)>;

// 事件类型转换工具
class EventTypeUtils {
public:
    static std::string toString(EventType type) {
        switch (type) {
            case EventType::SYSTEM_READY: return "system_ready";
            case EventType::SYSTEM_ERROR: return "system_error";
            case EventType::SYSTEM_RESTART: return "system_restart";
            case EventType::SYSTEM_SHUTDOWN: return "system_shutdown";
            
            case EventType::WIFI_CONNECTED: return "wifi_connected";
            case EventType::WIFI_DISCONNECTED: return "wifi_disconnected";
            case EventType::WEBSOCKET_CONNECTED: return "websocket_connected";
            case EventType::WEBSOCKET_DISCONNECTED: return "websocket_disconnected";
            case EventType::HTTP_REQUEST_RECEIVED: return "http_request_received";
            
            case EventType::SIGNAL_LEARNED: return "signal_learned";
            case EventType::SIGNAL_SENT: return "signal_sent";
            case EventType::SIGNAL_ADDED: return "signal_added";
            case EventType::SIGNAL_DELETED: return "signal_deleted";
            case EventType::SIGNAL_UPDATED: return "signal_updated";
            case EventType::SIGNAL_LEARNING_TIMEOUT: return "signal_learning_timeout";
            
            case EventType::LEARNING_START_REQUEST: return "learning_start_request";
            case EventType::LEARNING_STOP_REQUEST: return "learning_stop_request";
            case EventType::EMIT_SIGNAL_REQUEST: return "emit_signal_request";
            case EventType::EMIT_BATCH_REQUEST: return "emit_batch_request";
            
            case EventType::LEARNING_STARTED: return "learning_started";
            case EventType::LEARNING_STOPPED: return "learning_stopped";
            case EventType::EMIT_STARTED: return "emit_started";
            case EventType::EMIT_COMPLETED: return "emit_completed";
            case EventType::HARDWARE_ERROR: return "hardware_error";
            
            case EventType::STATUS_UPDATE: return "status_update";
            case EventType::ERROR_OCCURRED: return "error_occurred";
            case EventType::HEARTBEAT: return "heartbeat";
            
            case EventType::CORE_SYNC_REQUEST: return "core_sync_request";
            case EventType::CORE_SYNC_RESPONSE: return "core_sync_response";
            case EventType::INTER_CORE_MESSAGE: return "inter_core_message";
            
            default: return "unknown";
        }
    }

    static EventType fromString(const std::string& str) {
        if (str == "system_ready") return EventType::SYSTEM_READY;
        if (str == "system_error") return EventType::SYSTEM_ERROR;
        if (str == "wifi_connected") return EventType::WIFI_CONNECTED;
        if (str == "wifi_disconnected") return EventType::WIFI_DISCONNECTED;
        if (str == "signal_learned") return EventType::SIGNAL_LEARNED;
        if (str == "signal_sent") return EventType::SIGNAL_SENT;
        if (str == "status_update") return EventType::STATUS_UPDATE;
        // 可以继续添加更多映射
        return EventType::SYSTEM_ERROR; // 默认值
    }

    static EventPriority getPriority(EventType type) {
        switch (type) {
            case EventType::SYSTEM_ERROR:
            case EventType::HARDWARE_ERROR:
            case EventType::SYSTEM_SHUTDOWN:
                return EventPriority::CRITICAL;
                
            case EventType::EMIT_SIGNAL_REQUEST:
            case EventType::LEARNING_START_REQUEST:
            case EventType::LEARNING_STOP_REQUEST:
                return EventPriority::HIGH;
                
            case EventType::STATUS_UPDATE:
            case EventType::HEARTBEAT:
                return EventPriority::LOW;
                
            default:
                return EventPriority::NORMAL;
        }
    }

    static bool isInterCoreEvent(EventType type) {
        switch (type) {
            case EventType::LEARNING_START_REQUEST:
            case EventType::LEARNING_STOP_REQUEST:
            case EventType::EMIT_SIGNAL_REQUEST:
            case EventType::EMIT_BATCH_REQUEST:
            case EventType::SIGNAL_LEARNED:
            case EventType::SIGNAL_SENT:
            case EventType::LEARNING_STARTED:
            case EventType::LEARNING_STOPPED:
            case EventType::EMIT_STARTED:
            case EventType::EMIT_COMPLETED:
            case EventType::CORE_SYNC_REQUEST:
            case EventType::CORE_SYNC_RESPONSE:
            case EventType::INTER_CORE_MESSAGE:
                return true;
            default:
                return false;
        }
    }
};

#endif

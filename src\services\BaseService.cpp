/**
 * @file BaseService.cpp
 * @brief 服务基类实现
 * 
 * 实现服务框架的基础功能
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "BaseService.h"
#include "../core/DualCoreManager.h"

BaseService::BaseService(EventManager* em, const std::string& name, ServiceType type)
    : eventManager(em), serviceName(name), serviceStatus(ServiceStatus::STOPPED),
      serviceType(type), serviceInitialized(false), targetCore(0),
      serviceStartTime(0), lastLoopTime(0), loopCount(0) {
    
    // 根据服务类型设置目标核心
    switch (type) {
        case ServiceType::CORE0_SERVICE:
            targetCore = 0;
            break;
        case ServiceType::CORE1_SERVICE:
            targetCore = 1;
            break;
        case ServiceType::SHARED_SERVICE:
            targetCore = xPortGetCoreID(); // 当前核心
            break;
    }
    
    // 获取错误处理器
    if (g_dualCoreManager) {
        errorHandler = g_dualCoreManager->getErrorHandler();
    }
}

uint32_t BaseService::getUptime() const {
    if (serviceStartTime == 0) return 0;
    return millis() - serviceStartTime;
}

float BaseService::getLoopFrequency() const {
    uint32_t uptime = getUptime();
    if (uptime == 0 || loopCount == 0) return 0.0f;
    return (float)loopCount * 1000.0f / uptime;
}

void BaseService::logError(const std::string& operation, const std::string& error) {
    if (errorHandler) {
        errorHandler->logError(ErrorCategory::SERVICE, serviceName, 
                              operation + ": " + error);
    }
    Serial.printf("[%s] ERROR: %s - %s\n", serviceName.c_str(), 
                  operation.c_str(), error.c_str());
}

void BaseService::logWarning(const std::string& operation, const std::string& warning) {
    if (errorHandler) {
        errorHandler->logWarning(serviceName, operation + ": " + warning);
    }
    Serial.printf("[%s] WARNING: %s - %s\n", serviceName.c_str(), 
                  operation.c_str(), warning.c_str());
}

void BaseService::logInfo(const std::string& message) {
    if (errorHandler) {
        errorHandler->logInfo(serviceName, message);
    }
    Serial.printf("[%s] INFO: %s\n", serviceName.c_str(), message.c_str());
}

void BaseService::logDebug(const std::string& message) {
    if (errorHandler) {
        errorHandler->logDebug(serviceName, message);
    }
    Serial.printf("[%s] DEBUG: %s\n", serviceName.c_str(), message.c_str());
}

void BaseService::emitEvent(EventType type, const JsonDocument& data) {
    if (eventManager) {
        eventManager->emit(type, data);
    }
}

void BaseService::emitServiceEvent(const std::string& event, const JsonDocument& data) {
    if (eventManager) {
        JsonDocument eventData;
        eventData["service"] = serviceName;
        eventData["event"] = event;
        eventData["data"] = data;
        eventData["timestamp"] = millis();
        
        eventManager->emit(EventType::STATUS_UPDATE, eventData);
    }
}

void BaseService::sendToCore0(EventType type, const JsonDocument& data) {
    if (g_dualCoreManager) {
        EventData event(type, data);
        event.setSourceCore(xPortGetCoreID());
        event.setTargetCore(0);
        g_dualCoreManager->sendToCore0(event);
    }
}

void BaseService::sendToCore1(EventType type, const JsonDocument& data) {
    if (g_dualCoreManager) {
        EventData event(type, data);
        event.setSourceCore(xPortGetCoreID());
        event.setTargetCore(1);
        g_dualCoreManager->sendToCore1(event);
    }
}

void BaseService::broadcastEvent(EventType type, const JsonDocument& data) {
    if (g_dualCoreManager) {
        EventData event(type, data);
        event.setSourceCore(xPortGetCoreID());
        event.setTargetCore(255); // 广播
        g_dualCoreManager->broadcastEvent(event);
    }
}

void BaseService::setStatus(ServiceStatus status) {
    ServiceStatus oldStatus = serviceStatus;
    serviceStatus = status;
    
    if (oldStatus != status) {
        handleStatusChange(oldStatus, status);
        notifyStatusChange();
    }
}

void BaseService::setInitialized(bool initialized) {
    serviceInitialized = initialized;
    if (initialized && serviceStartTime == 0) {
        serviceStartTime = millis();
    }
}

void BaseService::setTargetCore(uint8_t core) {
    targetCore = core;
}

bool BaseService::startService() {
    logInfo("启动服务...");
    setStatus(ServiceStatus::STARTING);
    
    if (init()) {
        setStatus(ServiceStatus::RUNNING);
        setInitialized(true);
        logInfo("服务启动成功");
        return true;
    } else {
        setStatus(ServiceStatus::ERROR);
        logError("startService", "服务初始化失败");
        return false;
    }
}

void BaseService::stopService() {
    logInfo("停止服务...");
    setStatus(ServiceStatus::STOPPING);
    
    cleanup();
    
    setStatus(ServiceStatus::STOPPED);
    setInitialized(false);
    logInfo("服务已停止");
}

void BaseService::restartService() {
    logInfo("重启服务...");
    stopService();
    delay(100);
    startService();
}

uint32_t BaseService::subscribeEvent(EventType type, EventHandler handler) {
    if (eventManager) {
        return eventManager->on(type, handler);
    }
    return 0;
}

uint32_t BaseService::subscribeEventOnce(EventType type, EventHandler handler) {
    if (eventManager) {
        return eventManager->once(type, handler);
    }
    return 0;
}

void BaseService::unsubscribeEvent(EventType type, uint32_t subscriptionId) {
    if (eventManager) {
        eventManager->off(type, subscriptionId);
    }
}

void BaseService::updateLoopStats() {
    loopCount++;
    lastLoopTime = millis();
}

void BaseService::resetStats() {
    loopCount = 0;
    serviceStartTime = millis();
    lastLoopTime = 0;
}

void BaseService::handleStatusChange(ServiceStatus oldStatus, ServiceStatus newStatus) {
    // 子类可以重写此方法来处理状态变化
    logInfo("状态变化: " + ServiceUtils::statusToString(oldStatus) + 
            " -> " + ServiceUtils::statusToString(newStatus));
}

void BaseService::notifyStatusChange() {
    // 发送状态变化事件
    JsonDocument statusData = ServiceUtils::createServiceStatusEvent(this);
    emitServiceEvent("status_changed", statusData);
}

// ServiceUtils命名空间实现
namespace ServiceUtils {
    std::string statusToString(ServiceStatus status) {
        switch (status) {
            case ServiceStatus::STOPPED: return "stopped";
            case ServiceStatus::STARTING: return "starting";
            case ServiceStatus::RUNNING: return "running";
            case ServiceStatus::ERROR: return "error";
            case ServiceStatus::STOPPING: return "stopping";
            default: return "unknown";
        }
    }
    
    std::string typeToString(ServiceType type) {
        switch (type) {
            case ServiceType::CORE0_SERVICE: return "core0_service";
            case ServiceType::CORE1_SERVICE: return "core1_service";
            case ServiceType::SHARED_SERVICE: return "shared_service";
            default: return "unknown";
        }
    }
    
    ServiceStatus statusFromString(const std::string& str) {
        if (str == "stopped") return ServiceStatus::STOPPED;
        if (str == "starting") return ServiceStatus::STARTING;
        if (str == "running") return ServiceStatus::RUNNING;
        if (str == "error") return ServiceStatus::ERROR;
        if (str == "stopping") return ServiceStatus::STOPPING;
        return ServiceStatus::STOPPED;
    }
    
    ServiceType typeFromString(const std::string& str) {
        if (str == "core0_service") return ServiceType::CORE0_SERVICE;
        if (str == "core1_service") return ServiceType::CORE1_SERVICE;
        if (str == "shared_service") return ServiceType::SHARED_SERVICE;
        return ServiceType::SHARED_SERVICE;
    }
    
    bool isValidTransition(ServiceStatus from, ServiceStatus to) {
        // 定义有效的状态转换
        switch (from) {
            case ServiceStatus::STOPPED:
                return to == ServiceStatus::STARTING;
            case ServiceStatus::STARTING:
                return to == ServiceStatus::RUNNING || to == ServiceStatus::ERROR;
            case ServiceStatus::RUNNING:
                return to == ServiceStatus::STOPPING || to == ServiceStatus::ERROR;
            case ServiceStatus::ERROR:
                return to == ServiceStatus::STOPPING || to == ServiceStatus::STARTING;
            case ServiceStatus::STOPPING:
                return to == ServiceStatus::STOPPED;
            default:
                return false;
        }
    }
    
    JsonDocument createServiceStatusEvent(const BaseService* service) {
        JsonDocument doc;
        doc["name"] = service->getName();
        doc["status"] = statusToString(service->getStatus());
        doc["type"] = typeToString(service->getType());
        doc["initialized"] = service->isInitialized();
        doc["running"] = service->isRunning();
        doc["uptime"] = service->getUptime();
        doc["loop_count"] = service->getLoopCount();
        doc["loop_frequency"] = service->getLoopFrequency();
        doc["target_core"] = service->getTargetCore();
        doc["timestamp"] = millis();
        return doc;
    }
}

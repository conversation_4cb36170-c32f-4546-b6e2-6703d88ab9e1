/**
 * @file NetworkManager.cpp
 * @brief 网络管理器实现
 * 
 * 实现网络管理的基础功能
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "NetworkManager.h"

NetworkManager::NetworkManager() 
    : initialized(false), wifiConnected(false) {
}

NetworkManager::~NetworkManager() {
    cleanup();
}

bool NetworkManager::init() {
    Serial.println("[NetworkManager] 初始化网络管理器...");
    
    // 初始化WiFi
    WiFi.mode(WIFI_AP_STA);
    
    // 创建Web服务器
    webServer = std::make_unique<AsyncWebServer>(80);
    
    initialized = true;
    Serial.println("[NetworkManager] 网络管理器初始化完成");
    
    return true;
}

void NetworkManager::loop() {
    // 检查WiFi状态
    bool currentWiFiStatus = (WiFi.status() == WL_CONNECTED);
    if (currentWiFiStatus != wifiConnected) {
        wifiConnected = currentWiFiStatus;
        if (wifiConnected) {
            Serial.println("[NetworkManager] WiFi连接成功");
            Serial.print("[NetworkManager] IP地址: ");
            Serial.println(WiFi.localIP());
        } else {
            Serial.println("[NetworkManager] WiFi连接断开");
        }
    }
}

void NetworkManager::cleanup() {
    if (!initialized) return;
    
    Serial.println("[NetworkManager] 清理网络管理器...");
    
    // 停止Web服务器
    if (webServer) {
        webServer->end();
        webServer.reset();
    }
    
    // 断开WiFi
    WiFi.disconnect();
    
    initialized = false;
    Serial.println("[NetworkManager] 网络管理器清理完成");
}

; ESP32-S3红外控制系统 - PlatformIO配置文件
;
; 作者：ESP32-S3红外控制系统
; 版本：2.0
; 日期：2025-01-01

[platformio]
description = ESP32-S3红外控制系统 - 基于双核并行架构的红外控制系统
default_envs = esp32-s3-devkitc-1

[env:esp32-s3-devkitc-1]
platform = espressif32@6.11.0
board = esp32-s3-devkitc-1
framework = arduino

build_flags =
    -DCORE_DEBUG_LEVEL=3
    -DCONFIG_ARDUHAL_LOG_COLORS=1
    -O2
    -DARDUINO_USB_CDC_ON_BOOT=1
    -DARDUINO_USB_MODE=1
    -DSYSTEM_VERSION=\"2.0.0\"
    -DBUILD_TIMESTAMP=\"2025-01-01\"
    -DTARGET_BOARD=\"ESP32-S3-DevKitC-1\"

lib_deps =
    bblanchon/Ard<PERSON><PERSON><PERSON><PERSON>@^7.4.2
    ESP32Async/ESPAsyncWebServer@^3.7.9
    ESP32Async/AsyncTCP@^3.4.5
    crankyoldgit/IRremoteESP8266@^2.8.6

monitor_speed = 115200
monitor_filters = esp32_exception_decoder
upload_speed = 921600
upload_port = auto

; 调试环境 - 开发调试，启用严格警告检查
[env:esp32-s3-devkitc-1-debug]
extends = env:esp32-s3-devkitc-1
build_type = debug
build_flags =
    ${env:esp32-s3-devkitc-1.build_flags}
    -DDEBUG_MODE=1
    -DCORE_DEBUG_LEVEL=5
    -g3
    -O0
    -Wall
    -Wextra
    -Werror

; 发布环境 - 生产部署
[env:esp32-s3-devkitc-1-release]
extends = env:esp32-s3-devkitc-1
build_type = release
build_flags =
    ${env:esp32-s3-devkitc-1.build_flags}
    -DRELEASE_MODE=1
    -DCORE_DEBUG_LEVEL=1
    -Os
    -DNDEBUG

; OTA环境 - 无线更新
[env:esp32-s3-devkitc-1-ota]
extends = env:esp32-s3-devkitc-1-release
upload_protocol = espota
upload_port = esp32-ir-controller.local
upload_flags =
    --port=3232
    --auth=ota_secure_2024



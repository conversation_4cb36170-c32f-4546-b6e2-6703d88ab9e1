/**
 * @file StatusService.h
 * @brief 状态管理服务 - 系统状态监控和报告
 * 
 * 基于前端完整数据文档的状态查询需求
 * 支持系统状态、性能监控、WebSocket状态广播
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef STATUS_SERVICE_H
#define STATUS_SERVICE_H

#include "BaseService.h"
#include "../types/APITypes.h"

class StatusService : public BaseService {
private:
    // 状态缓存
    SystemStatus cachedStatus;
    uint32_t lastStatusUpdate;
    uint32_t statusUpdateInterval;
    
    // 性能监控
    uint32_t cpuUsageHistory[10];
    uint32_t memoryUsageHistory[10];
    uint8_t historyIndex;
    
    // 网络状态
    bool wifiConnected;
    int32_t wifiRSSI;
    uint32_t wifiConnectTime;
    
    // 系统统计
    uint32_t totalRequests;
    uint32_t totalErrors;
    uint32_t totalSignalsSent;
    uint32_t totalSignalsLearned;

public:
    StatusService(EventManager* em);
    virtual ~StatusService() = default;

    // BaseService接口实现
    bool init() override;
    void loop() override;
    void cleanup() override;

    // 状态查询接口 (匹配前端GET /api/status)
    SystemStatus getSystemStatus();
    JsonDocument getSystemStatusJson();
    
    // 详细状态信息
    JsonDocument getDetailedStatus();
    JsonDocument getPerformanceStats();
    JsonDocument getNetworkStatus();
    JsonDocument getHardwareStatus();
    
    // 统计信息
    JsonDocument getStatistics();
    void incrementRequestCount() { totalRequests++; }
    void incrementErrorCount() { totalErrors++; }
    void incrementSignalsSent() { totalSignalsSent++; }
    void incrementSignalsLearned() { totalSignalsLearned++; }
    
    // 网络状态更新
    void updateWiFiStatus(bool connected, int32_t rssi = 0);
    void updateNetworkStatus(const std::string& status);
    
    // 状态广播控制
    void enableStatusBroadcast(bool enable);
    void setStatusUpdateInterval(uint32_t intervalMs);
    void forceStatusUpdate();

private:
    // 状态更新
    void updateSystemStatus();
    void updatePerformanceMetrics();
    void updateMemoryUsage();
    void updateCPUUsage();
    void updateNetworkMetrics();
    void updateHardwareMetrics();
    
    // 历史数据管理
    void addToHistory(uint32_t* history, uint32_t value);
    float calculateAverage(const uint32_t* history, size_t size);
    
    // WebSocket广播 (匹配前端status_update事件)
    void broadcastStatusUpdate();
    JsonDocument createStatusUpdateEvent();
    
    // 系统信息获取
    uint32_t getUptime();
    float getMemoryUsage();
    float getCPUUsage();
    uint32_t getFreeHeap();
    float getChipTemperature();
    
    // 网络信息获取
    std::string getWiFiStatus();
    int32_t getWiFiStrength();
    std::string getLocalIP();
    std::string getMACAddress();
    
    // 硬件信息获取
    std::string getChipModel();
    uint32_t getFlashSize();
    uint32_t getCPUFrequency();
    std::string getFirmwareVersion();
};

#endif

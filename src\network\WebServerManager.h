/**
 * @file WebServerManager.h
 * @brief Web服务器管理器 - HTTP服务管理
 * 
 * 基于前端完整数据文档的HTTP API需求
 * 支持静态文件服务、API路由、CORS
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include "../services/BaseService.h"
#include <ESPAsyncWebServer.h>
#include <memory>

// 前向声明
class APIRouter;

class WebServerManager : public BaseService {
private:
    std::unique_ptr<AsyncWebServer> server;
    std::unique_ptr<APIRouter> apiRouter;
    
    uint16_t serverPort;
    bool serverStarted;
    bool corsEnabled;
    
    // 统计信息
    uint32_t totalRequests;
    uint32_t totalErrors;
    uint32_t activeConnections;

public:
    WebServerManager(EventManager* em);
    virtual ~WebServerManager();

    // BaseService接口实现
    bool init() override;
    void loop() override;
    void cleanup() override;

    // 服务器控制
    bool startServer(uint16_t port = 80);
    void stopServer();
    bool isRunning() const { return serverStarted; }
    
    // 路由管理
    void setupRoutes();
    void setupStaticFiles();
    void setupCORS();
    void setup404Handler();
    
    // 获取服务器实例
    AsyncWebServer* getServer() { return server.get(); }
    
    // 统计信息
    uint32_t getTotalRequests() const { return totalRequests; }
    uint32_t getTotalErrors() const { return totalErrors; }
    uint32_t getActiveConnections() const { return activeConnections; }

private:
    // 请求处理
    void handleRequest(AsyncWebServerRequest* request);
    void handleNotFound(AsyncWebServerRequest* request);
    void handleOptions(AsyncWebServerRequest* request);
    
    // 中间件
    void addCORSHeaders(AsyncWebServerResponse* response);
    void logRequest(AsyncWebServerRequest* request);
    
    // 统计更新
    void incrementRequestCount();
    void incrementErrorCount();
    void updateConnectionCount();
};

#endif

/**
 * @file IRTransmitter.h
 * @brief 红外发射器 - Core 1硬件控制
 * 
 * 基于前端完整数据文档的信号发射需求
 * 支持多种红外协议、重复发射、精确时序
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef IR_TRANSMITTER_H
#define IR_TRANSMITTER_H

#include <Arduino.h>
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include "../types/SignalData.h"
#include "../core/EventManager.h"
#include <string>

// 发射状态
enum class TransmitState {
    IDLE,
    TRANSMITTING,
    COMPLETED,
    ERROR
};

// 发射任务
struct TransmitTask {
    SignalData signal;
    uint8_t repeatCount;
    uint8_t currentRepeat;
    uint16_t interval;
    uint32_t startTime;
    uint32_t lastTransmit;
    bool active;
    
    TransmitTask() : repeatCount(1), currentRepeat(0), interval(100), 
                     startTime(0), lastTransmit(0), active(false) {}
};

class IRTransmitter {
private:
    IRsend* irSend;
    uint8_t transmitPin;
    uint16_t frequency;
    EventManager* eventManager;
    
    // 发射状态
    TransmitState currentState;
    TransmitTask currentTask;
    
    // 统计信息
    uint32_t totalTransmissions;
    uint32_t successfulTransmissions;
    uint32_t failedTransmissions;

public:
    IRTransmitter(EventManager* em);
    ~IRTransmitter();

    // 生命周期
    bool init(uint8_t pin, uint16_t freq = 38000);
    void cleanup();
    void loop();

    // 信号发射接口 (匹配前端API需求)
    bool transmitSignal(const SignalData& signal, uint8_t repeat = 1);
    bool transmitSignalById(const std::string& signalId, uint8_t repeat = 1);
    bool transmitRawData(const std::string& data, const std::string& protocol, 
                        uint16_t frequency, uint8_t repeat = 1);
    
    // 批量发射
    bool transmitBatch(const std::vector<SignalData>& signals, 
                      uint8_t repeat = 1, uint16_t interval = 1000);
    
    // 状态查询
    TransmitState getState() const { return currentState; }
    bool isTransmitting() const { return currentState == TransmitState::TRANSMITTING; }
    bool isBusy() const { return currentTask.active; }
    
    // 配置管理
    bool setFrequency(uint16_t freq);
    uint16_t getFrequency() const { return frequency; }
    bool setTransmitPin(uint8_t pin);
    uint8_t getTransmitPin() const { return transmitPin; }
    
    // 统计信息
    uint32_t getTotalTransmissions() const { return totalTransmissions; }
    uint32_t getSuccessfulTransmissions() const { return successfulTransmissions; }
    uint32_t getFailedTransmissions() const { return failedTransmissions; }
    float getSuccessRate() const;
    
    // 硬件测试
    bool testTransmitter();
    bool isHardwareReady();

private:
    // 发射处理
    void processTransmission();
    bool executeTransmission(const SignalData& signal);
    void completeTransmission(bool success);
    void handleTransmissionError(const std::string& error);
    
    // 协议处理
    bool transmitNEC(const std::string& data);
    bool transmitRC5(const std::string& data);
    bool transmitSONY(const std::string& data);
    bool transmitRAW(const std::string& data);
    bool transmitGeneric(const std::string& data, const std::string& protocol);
    
    // 数据转换
    uint64_t hexStringToUint64(const std::string& hexStr);
    std::vector<uint16_t> parseRawData(const std::string& rawData);
    bool validateSignalData(const SignalData& signal);
    
    // 状态管理
    void setState(TransmitState state);
    void resetTask();
    void updateStatistics(bool success);
    
    // 事件发布 (匹配前端WebSocket事件)
    void emitTransmissionStarted(const SignalData& signal);
    void emitTransmissionCompleted(const SignalData& signal, bool success, uint32_t duration);
    void emitTransmissionError(const SignalData& signal, const std::string& error);
    
    // 硬件控制
    bool initializeHardware();
    void enableTransmitter();
    void disableTransmitter();
    
    // 错误处理
    void logTransmissionError(const std::string& operation, const std::string& error);
    void handleHardwareError(const std::string& error);
};

#endif

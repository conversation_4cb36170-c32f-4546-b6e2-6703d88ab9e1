/**
 * @file HardwareManager.h
 * @brief 硬件管理器 - Core 1硬件控制管理
 * 
 * 管理红外发射接收、LED、按键等硬件功能
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef HARDWARE_MANAGER_H
#define HARDWARE_MANAGER_H

#include <Arduino.h>
#include <memory>

class HardwareManager {
private:
    bool initialized;

public:
    HardwareManager();
    ~HardwareManager();

    bool init();
    void loop();
    void cleanup();

    bool isInitialized() const { return initialized; }
};

#endif

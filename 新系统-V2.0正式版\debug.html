<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块加载调试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>模块加载调试页面</h1>
    <div id="status"></div>
    
    <!-- 按照相同顺序加载脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/data-validator.js"></script>
    <script src="js/core.js"></script>
    
    <!-- 性能优化组件 -->
    <script src="js/dom-update-manager.js"></script>
    <script src="js/optimized-signal-storage.js"></script>
    <script src="js/unified-timer-manager.js"></script>
    <script src="js/virtual-scroll-list.js"></script>
    <script src="js/signal-virtual-list.js"></script>
  
    <script src="js/signal-manager.js"></script>
    <script src="js/control-module.js"></script>
    <script src="js/timer-settings.js"></script>
    <script src="js/status-display.js"></script>
    <script src="js/system-monitor.js"></script>
    
    <script>
        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            document.getElementById('status').appendChild(div);
        }
        
        function checkModules() {
            const modules = [
                'R1Utils',
                'DataValidator', 
                'EventBus',
                'ESP32Communicator',
                'BaseModule',
                'DOMUpdateManager',
                'OptimizedSignalStorage',
                'UnifiedTimerManager',
                'VirtualScrollList',
                'SignalVirtualList',
                'SignalManager',
                'ControlModule',
                'TimerSettings',
                'StatusDisplay',
                'SystemMonitor'
            ];
            
            addStatus('开始检查模块加载状态...', 'info');
            
            modules.forEach(moduleName => {
                if (typeof window[moduleName] === 'function') {
                    addStatus(`✅ ${moduleName} - 已加载`, 'success');
                } else if (typeof window[moduleName] !== 'undefined') {
                    addStatus(`⚠️ ${moduleName} - 已定义但不是函数: ${typeof window[moduleName]}`, 'warning');
                } else {
                    addStatus(`❌ ${moduleName} - 未定义`, 'error');
                }
            });
            
            // 检查window对象上的所有属性
            const windowProps = Object.keys(window).filter(key => 
                key.includes('Module') || 
                key.includes('Manager') || 
                key.includes('Settings') || 
                key.includes('Display') || 
                key.includes('Monitor') ||
                key.includes('Utils') ||
                key.includes('Validator')
            );
            
            addStatus(`Window对象上的相关属性: ${windowProps.join(', ')}`, 'info');
        }
        
        // 等待所有脚本加载完成
        window.addEventListener('load', () => {
            setTimeout(checkModules, 100);
        });
    </script>
</body>
</html>

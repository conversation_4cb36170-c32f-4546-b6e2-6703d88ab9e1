/**
 * @file DataValidator.h
 * @brief 数据验证器 - 数据完整性和有效性验证
 * 
 * 基于前端完整数据文档的数据验证需求
 * 支持信号数据、配置数据、API请求验证
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef DATA_VALIDATOR_H
#define DATA_VALIDATOR_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <string>
#include <vector>
#include "../types/SignalData.h"
#include "../types/TaskData.h"
#include "../types/APITypes.h"

// 验证结果
struct ValidationResult {
    bool isValid;
    std::vector<std::string> errors;
    std::vector<std::string> warnings;
    
    ValidationResult() : isValid(true) {}
    
    void addError(const std::string& error) {
        errors.push_back(error);
        isValid = false;
    }
    
    void addWarning(const std::string& warning) {
        warnings.push_back(warning);
    }
    
    bool hasErrors() const { return !errors.empty(); }
    bool hasWarnings() const { return !warnings.empty(); }
    
    std::string getErrorsString() const {
        std::string result;
        for (const auto& error : errors) {
            if (!result.empty()) result += "; ";
            result += error;
        }
        return result;
    }
};

class DataValidator {
public:
    // 信号数据验证 (匹配前端SignalData格式)
    static ValidationResult validateSignalData(const SignalData& signal);
    static ValidationResult validateSignalJson(const JsonObject& json);
    static bool isValidSignalId(const std::string& id);
    static bool isValidSignalType(const std::string& type);
    static bool isValidProtocol(const std::string& protocol);
    static bool isValidFrequency(uint16_t frequency);
    static bool isValidIRData(const std::string& data, const std::string& protocol);
    
    // 任务数据验证
    static ValidationResult validateTaskData(const TaskData& task);
    static ValidationResult validateTaskJson(const JsonObject& json);
    static bool isValidTaskId(const std::string& id);
    static bool isValidPriority(uint8_t priority);
    
    // API请求验证
    static ValidationResult validateLearningRequest(const LearningRequest& request);
    static ValidationResult validateEmitRequest(const EmitRequest& request);
    static ValidationResult validateBatchRequest(const BatchRequest& request);
    
    // 网络配置验证
    static ValidationResult validateNetworkConfig(const JsonObject& config);
    static bool isValidSSID(const std::string& ssid);
    static bool isValidPassword(const std::string& password);
    static bool isValidIPAddress(const std::string& ip);
    static bool isValidPort(uint16_t port);
    
    // 硬件配置验证
    static ValidationResult validateHardwareConfig(const JsonObject& config);
    static bool isValidPin(uint8_t pin);
    static bool isValidDutyCycle(uint8_t dutyCycle);
    
    // 通用验证
    static bool isValidString(const std::string& str, size_t minLen = 0, size_t maxLen = SIZE_MAX);
    static bool isValidNumber(int64_t value, int64_t min = INT64_MIN, int64_t max = INT64_MAX);
    static bool isValidTimestamp(uint64_t timestamp);
    static bool isValidJSON(const std::string& jsonStr);
    
    // 数据格式验证
    static bool isValidHexString(const std::string& hex);
    static bool isValidBase64(const std::string& base64);
    static bool isValidMACAddress(const std::string& mac);
    static bool isValidURL(const std::string& url);
    
    // 安全验证
    static bool isSafeString(const std::string& str);
    static bool containsSQLInjection(const std::string& str);
    static bool containsXSS(const std::string& str);
    static bool isValidFilename(const std::string& filename);
    
    // 业务逻辑验证
    static ValidationResult validateSignalLimits(size_t currentCount, size_t maxCount);
    static ValidationResult validateMemoryUsage(size_t currentUsage, size_t maxUsage);
    static ValidationResult validateSystemResources();

private:
    // 内部验证辅助
    static bool validateStringLength(const std::string& str, size_t min, size_t max);
    static bool validateNumberRange(int64_t value, int64_t min, int64_t max);
    static bool validateEnum(const std::string& value, const std::vector<std::string>& validValues);
    
    // 正则表达式验证
    static bool matchesPattern(const std::string& str, const std::string& pattern);
    static bool isAlphanumeric(const std::string& str);
    static bool isNumeric(const std::string& str);
    
    // 特定格式验证
    static bool validateIPv4(const std::string& ip);
    static bool validateSubnetMask(const std::string& mask);
    static bool validateDNS(const std::string& dns);
    
    // 硬件相关验证
    static bool isValidESP32Pin(uint8_t pin);
    static bool isPinAvailable(uint8_t pin);
    static bool validatePinConflicts(const std::vector<uint8_t>& pins);
    
    // 错误消息生成
    static std::string formatError(const std::string& field, const std::string& issue);
    static std::string formatWarning(const std::string& field, const std::string& issue);
};

// 验证宏定义
#define VALIDATE_SIGNAL(signal) DataValidator::validateSignalData(signal)
#define VALIDATE_TASK(task) DataValidator::validateTaskData(task)
#define VALIDATE_LEARNING_REQUEST(request) DataValidator::validateLearningRequest(request)
#define VALIDATE_EMIT_REQUEST(request) DataValidator::validateEmitRequest(request)

#define IS_VALID_SIGNAL_ID(id) DataValidator::isValidSignalId(id)
#define IS_VALID_SIGNAL_TYPE(type) DataValidator::isValidSignalType(type)
#define IS_VALID_PROTOCOL(protocol) DataValidator::isValidProtocol(protocol)
#define IS_VALID_FREQUENCY(freq) DataValidator::isValidFrequency(freq)

#define IS_SAFE_STRING(str) DataValidator::isSafeString(str)
#define IS_VALID_JSON(json) DataValidator::isValidJSON(json)

#endif

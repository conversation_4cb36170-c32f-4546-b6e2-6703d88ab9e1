/**
 * @file SystemConfig.h
 * @brief 系统配置管理 - 统一配置存储和管理
 * 
 * 基于前端完整数据文档的配置需求设计
 * 支持网络、硬件、系统、服务器配置管理
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef SYSTEM_CONFIG_H
#define SYSTEM_CONFIG_H

#include <Arduino.h>
#include <Preferences.h>
#include <ArduinoJson.h>
#include <string>

// 网络配置结构
struct NetworkConfig {
    // AP模式配置
    std::string apSSID = "ESP32_IR_Controller";
    std::string apPassword = "12345678";
    uint8_t apChannel = 1;
    uint8_t apMaxConnections = 4;
    bool apHidden = false;

    // WiFi配置
    std::string wifiSSID = "";
    std::string wifiPassword = "";
    bool wifiEnabled = false;
    bool staticIP = false;
    std::string ipAddress = "*************";
    std::string gateway = "***********";
    std::string subnet = "*************";
    std::string dns1 = "*******";
    std::string dns2 = "*******";
};

// 硬件配置结构
struct HardwareConfig {
    uint8_t irTransmitPin = 18;
    uint8_t irReceivePin = 19;
    uint8_t statusLEDPin = 2;
    uint8_t learnButtonPin = 0;
    uint16_t irFrequency = 38000;
    uint8_t irDutyCycle = 33;
    bool ledInverted = false;
    bool buttonPullup = true;
};

// 系统设置结构
struct SystemSettings {
    std::string deviceName = "红外控制器";
    std::string deviceDescription = "ESP32-S3红外控制系统";
    std::string deviceLocation = "客厅";
    int8_t timezone = 8;
    uint8_t logLevel = 3;
    bool autoSave = true;
    bool ledEnabled = true;
    uint32_t learningTimeout = 30000;
    uint32_t emitTimeout = 5000;
    uint16_t maxSignals = 1000;
};

// 服务器配置结构
struct ServerConfig {
    uint16_t httpPort = 80;
    uint16_t websocketPort = 81;
    uint16_t maxConnections = 10;
    uint32_t requestTimeout = 5000;
    bool corsEnabled = true;
    std::string corsOrigin = "*";
};

class SystemConfig {
private:
    Preferences preferences;
    NetworkConfig network;
    HardwareConfig hardware;
    SystemSettings system;
    ServerConfig server;
    bool configLoaded;
    bool configChanged;

public:
    SystemConfig();
    ~SystemConfig();

    // 生命周期
    bool init();
    void cleanup();

    // 配置访问
    const NetworkConfig& getNetwork() const { return network; }
    const HardwareConfig& getHardware() const { return hardware; }
    const SystemSettings& getSystem() const { return system; }
    const ServerConfig& getServer() const { return server; }

    // 配置修改
    bool updateNetwork(const NetworkConfig& config);
    bool updateHardware(const HardwareConfig& config);
    bool updateSystem(const SystemSettings& config);
    bool updateServer(const ServerConfig& config);

    // 持久化
    bool saveConfig();
    bool loadConfig();
    bool resetToDefaults();

    // JSON序列化
    JsonDocument toJson() const;
    bool fromJson(const JsonObject& json);

    // 状态查询
    bool isLoaded() const { return configLoaded; }
    bool hasChanges() const { return configChanged; }

    // 配置验证
    bool validateConfig() const;
    std::string getValidationErrors() const;

private:
    // 内部方法
    void setDefaults();
    bool saveNetworkConfig();
    bool saveHardwareConfig();
    bool saveSystemConfig();
    bool saveServerConfig();
    bool loadNetworkConfig();
    bool loadHardwareConfig();
    bool loadSystemConfig();
    bool loadServerConfig();
    
    // 验证方法
    bool validateNetworkConfig() const;
    bool validateHardwareConfig() const;
    bool validateSystemConfig() const;
    bool validateServerConfig() const;
    
    // JSON转换
    JsonObject networkToJson() const;
    JsonObject hardwareToJson() const;
    JsonObject systemToJson() const;
    JsonObject serverToJson() const;
    bool networkFromJson(const JsonObject& json);
    bool hardwareFromJson(const JsonObject& json);
    bool systemFromJson(const JsonObject& json);
    bool serverFromJson(const JsonObject& json);
};

// 全局配置实例
extern SystemConfig* g_systemConfig;

// 便捷访问宏
#define GET_NETWORK_CONFIG() (g_systemConfig ? g_systemConfig->getNetwork() : NetworkConfig())
#define GET_HARDWARE_CONFIG() (g_systemConfig ? g_systemConfig->getHardware() : HardwareConfig())
#define GET_SYSTEM_CONFIG() (g_systemConfig ? g_systemConfig->getSystem() : SystemSettings())
#define GET_SERVER_CONFIG() (g_systemConfig ? g_systemConfig->getServer() : ServerConfig())

// 配置常量
namespace ConfigConstants {
    // 网络相关
    const uint16_t MIN_PORT = 1024;
    const uint16_t MAX_PORT = 65535;
    const uint8_t MIN_CHANNEL = 1;
    const uint8_t MAX_CHANNEL = 13;
    const uint8_t MIN_CONNECTIONS = 1;
    const uint8_t MAX_CONNECTIONS = 10;
    
    // 硬件相关
    const uint8_t MIN_PIN = 0;
    const uint8_t MAX_PIN = 48;
    const uint16_t MIN_FREQUENCY = 30000;
    const uint16_t MAX_FREQUENCY = 60000;
    const uint8_t MIN_DUTY_CYCLE = 10;
    const uint8_t MAX_DUTY_CYCLE = 90;
    
    // 系统相关
    const int8_t MIN_TIMEZONE = -12;
    const int8_t MAX_TIMEZONE = 14;
    const uint8_t MIN_LOG_LEVEL = 0;
    const uint8_t MAX_LOG_LEVEL = 4;
    const uint32_t MIN_TIMEOUT = 1000;
    const uint32_t MAX_TIMEOUT = 300000;
    const uint16_t MIN_SIGNALS = 10;
    const uint16_t MAX_SIGNALS = 2000;
    
    // 字符串长度限制
    const size_t MAX_SSID_LENGTH = 32;
    const size_t MAX_PASSWORD_LENGTH = 64;
    const size_t MAX_NAME_LENGTH = 32;
    const size_t MAX_DESCRIPTION_LENGTH = 128;
    const size_t MAX_LOCATION_LENGTH = 64;
    const size_t MAX_ORIGIN_LENGTH = 128;
}

#endif

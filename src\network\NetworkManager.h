/**
 * @file NetworkManager.h
 * @brief 网络管理器 - Core 0网络通信管理
 * 
 * 管理WiFi连接、HTTP服务器、WebSocket等网络功能
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef NETWORK_MANAGER_H
#define NETWORK_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <ESPAsyncWebServer.h>
#include <memory>

class NetworkManager {
private:
    bool initialized;
    bool wifiConnected;
    std::unique_ptr<AsyncWebServer> webServer;

public:
    NetworkManager();
    ~NetworkManager();

    bool init();
    void loop();
    void cleanup();

    bool isInitialized() const { return initialized; }
    bool isWiFiConnected() const { return wifiConnected; }
};

#endif

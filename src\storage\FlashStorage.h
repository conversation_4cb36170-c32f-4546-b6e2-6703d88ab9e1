/**
 * @file FlashStorage.h
 * @brief Flash存储管理器 - SPIFFS文件系统操作
 * 
 * 基于前端完整数据文档的持久化存储需求
 * 支持文件读写、目录管理、存储优化
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef FLASH_STORAGE_H
#define FLASH_STORAGE_H

#include <Arduino.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include <string>
#include <vector>

// 文件信息结构
struct FileInfo {
    std::string name;
    size_t size;
    time_t lastModified;
    bool isDirectory;
    
    FileInfo() : size(0), lastModified(0), isDirectory(false) {}
};

// 存储配置
struct StorageConfig {
    std::string signalsPath = "/signals";
    std::string configPath = "/config";
    std::string logsPath = "/logs";
    std::string backupPath = "/backup";
    size_t maxFileSize = 1024 * 1024;  // 1MB
    uint32_t backupInterval = 3600000;  // 1小时
    bool autoBackup = true;
    bool compression = false;
};

class FlashStorage {
private:
    StorageConfig config;
    bool initialized;
    
    // 统计信息
    uint32_t totalReads;
    uint32_t totalWrites;
    uint32_t totalDeletes;
    uint32_t totalErrors;
    size_t totalBytesRead;
    size_t totalBytesWritten;

public:
    FlashStorage();
    ~FlashStorage();

    // 生命周期
    bool init();
    void cleanup();

    // 文件操作
    bool writeFile(const std::string& path, const std::string& content);
    bool writeFile(const std::string& path, const JsonDocument& json);
    std::string readFile(const std::string& path);
    JsonDocument readJsonFile(const std::string& path);
    bool deleteFile(const std::string& path);
    bool fileExists(const std::string& path);
    
    // 目录操作
    bool createDirectory(const std::string& path);
    bool deleteDirectory(const std::string& path);
    bool directoryExists(const std::string& path);
    std::vector<FileInfo> listDirectory(const std::string& path);
    
    // 批量操作
    bool writeFiles(const std::vector<std::pair<std::string, std::string>>& files);
    std::vector<std::string> readFiles(const std::vector<std::string>& paths);
    bool deleteFiles(const std::vector<std::string>& paths);
    
    // 文件信息
    FileInfo getFileInfo(const std::string& path);
    size_t getFileSize(const std::string& path);
    time_t getFileModTime(const std::string& path);
    
    // 存储空间管理
    size_t getTotalSpace();
    size_t getUsedSpace();
    size_t getFreeSpace();
    float getUsagePercentage();
    
    // 备份和恢复
    bool createBackup(const std::string& sourcePath, const std::string& backupPath);
    bool restoreBackup(const std::string& backupPath, const std::string& targetPath);
    bool autoBackup();
    std::vector<std::string> listBackups();
    
    // 配置管理
    void setConfig(const StorageConfig& cfg) { config = cfg; }
    const StorageConfig& getConfig() const { return config; }
    
    // 统计信息
    uint32_t getTotalReads() const { return totalReads; }
    uint32_t getTotalWrites() const { return totalWrites; }
    uint32_t getTotalDeletes() const { return totalDeletes; }
    uint32_t getTotalErrors() const { return totalErrors; }
    size_t getTotalBytesRead() const { return totalBytesRead; }
    size_t getTotalBytesWritten() const { return totalBytesWritten; }
    JsonDocument getStatsJson() const;
    
    // 维护操作
    bool format();
    bool defragment();
    bool checkIntegrity();
    void cleanup();

private:
    // 内部文件操作
    bool writeFileInternal(const std::string& path, const uint8_t* data, size_t size);
    bool readFileInternal(const std::string& path, std::vector<uint8_t>& data);
    
    // 路径处理
    std::string normalizePath(const std::string& path);
    std::string getDirectoryPath(const std::string& filePath);
    std::string getFileName(const std::string& filePath);
    bool isValidPath(const std::string& path);
    
    // 错误处理
    void handleError(const std::string& operation, const std::string& path);
    void logOperation(const std::string& operation, const std::string& path, bool success);
    
    // 统计更新
    void updateReadStats(size_t bytes);
    void updateWriteStats(size_t bytes);
    void updateDeleteStats();
    void updateErrorStats();
    
    // 空间管理
    bool hasEnoughSpace(size_t requiredBytes);
    void cleanupOldFiles();
    void optimizeStorage();
    
    // 备份辅助
    std::string generateBackupName(const std::string& originalPath);
    bool isBackupFile(const std::string& path);
    time_t parseBackupTime(const std::string& backupName);
};

// 全局Flash存储实例
extern FlashStorage* g_flashStorage;

// 便捷访问宏
#define WRITE_FILE(path, content) (g_flashStorage ? g_flashStorage->writeFile(path, content) : false)
#define READ_FILE(path) (g_flashStorage ? g_flashStorage->readFile(path) : std::string())
#define DELETE_FILE(path) (g_flashStorage ? g_flashStorage->deleteFile(path) : false)
#define FILE_EXISTS(path) (g_flashStorage ? g_flashStorage->fileExists(path) : false)

#endif

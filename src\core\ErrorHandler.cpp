/**
 * @file ErrorHandler.cpp
 * @brief 错误处理器实现 - 统一错误管理和日志记录
 * 
 * 实现错误记录、分级处理、WebSocket广播
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "ErrorHandler.h"
#include "../types/EventTypes.h"

// 全局实例
ErrorHandler* g_errorHandler = nullptr;

ErrorHandler::ErrorHandler() 
    : errorMutex(nullptr), logLevel(ErrorLevel::INFO), maxHistorySize(100),
      maxQueueSize(50), enableWebSocketBroadcast(true), enableSerialOutput(true),
      initialized(false), nextErrorId(1), totalErrors(0), criticalErrors(0) {
    
    // 初始化统计数组
    errorCountByLevel.resize(5, 0);  // 5个错误级别
    errorCountByCategory.resize(8, 0);  // 8个错误类别
    
    g_errorHandler = this;
}

ErrorHandler::~ErrorHandler() {
    cleanup();
    g_errorHandler = nullptr;
}

bool ErrorHandler::init() {
    Serial.println("[ErrorHandler] 初始化错误处理器...");
    
    // 创建互斥信号量
    errorMutex = xSemaphoreCreateMutex();
    if (errorMutex == nullptr) {
        Serial.println("[ErrorHandler] 互斥量创建失败");
        return false;
    }
    
    // 清空历史记录
    clearErrorHistory();
    clearErrorQueue();
    
    // 重置统计
    totalErrors = 0;
    criticalErrors = 0;
    nextErrorId = 1;
    
    initialized = true;
    Serial.println("[ErrorHandler] 错误处理器初始化完成");
    
    return true;
}

void ErrorHandler::cleanup() {
    if (!initialized) return;
    
    Serial.println("[ErrorHandler] 清理错误处理器...");
    
    initialized = false;
    
    // 清理队列和历史
    clearErrorQueue();
    clearErrorHistory();
    
    // 删除互斥量
    if (errorMutex) {
        vSemaphoreDelete(errorMutex);
        errorMutex = nullptr;
    }
    
    Serial.println("[ErrorHandler] 错误处理器清理完成");
}

void ErrorHandler::loop() {
    if (!initialized) return;
    
    // 处理错误队列
    processErrorQueue();
    
    // 定期清理旧错误
    static uint32_t lastCleanup = 0;
    if (millis() - lastCleanup > 60000) { // 每分钟清理一次
        cleanupOldErrors();
        lastCleanup = millis();
    }
}

void ErrorHandler::logError(const std::string& source, const std::string& message, 
                           const std::string& details) {
    logError(ErrorLevel::ERROR, ErrorCategory::SYSTEM, source, message, details);
}

void ErrorHandler::logError(ErrorCategory category, const std::string& source, 
                           const std::string& message, const std::string& details) {
    logError(ErrorLevel::ERROR, category, source, message, details);
}

void ErrorHandler::logError(ErrorLevel level, ErrorCategory category, 
                           const std::string& source, const std::string& message, 
                           const std::string& details) {
    if (!initialized || level < logLevel) return;
    
    // 创建错误记录
    ErrorRecord error;
    error.id = nextErrorId++;
    error.level = level;
    error.category = category;
    error.source = source;
    error.message = message;
    error.details = details;
    error.timestamp = millis();
    error.coreId = xPortGetCoreID();
    error.freeHeap = ESP.getFreeHeap();
    
    if (lockMutex()) {
        // 添加到队列
        if (errorQueue.size() < maxQueueSize) {
            errorQueue.push(error);
        }
        
        // 更新统计
        totalErrors++;
        if (level == ErrorLevel::CRITICAL) {
            criticalErrors++;
        }
        
        errorCountByLevel[static_cast<int>(level)]++;
        errorCountByCategory[static_cast<int>(category)]++;
        
        unlockMutex();
    }
    
    // 立即输出到串口
    if (enableSerialOutput) {
        outputToSerial(error);
    }
    
    // 关键错误立即处理
    if (level == ErrorLevel::CRITICAL) {
        addErrorToHistory(error);
        broadcastError(error);
    }
}

void ErrorHandler::logDebug(const std::string& source, const std::string& message) {
    logError(ErrorLevel::DEBUG, ErrorCategory::SYSTEM, source, message);
}

void ErrorHandler::logInfo(const std::string& source, const std::string& message) {
    logError(ErrorLevel::INFO, ErrorCategory::SYSTEM, source, message);
}

void ErrorHandler::logWarning(const std::string& source, const std::string& message) {
    logError(ErrorLevel::WARNING, ErrorCategory::SYSTEM, source, message);
}

void ErrorHandler::logCritical(const std::string& source, const std::string& message) {
    logError(ErrorLevel::CRITICAL, ErrorCategory::SYSTEM, source, message);
}

void ErrorHandler::handleError(const std::string& source, const std::string& error) {
    logError(ErrorLevel::ERROR, ErrorCategory::SYSTEM, source, error);
}

void ErrorHandler::handleCriticalError(const std::string& source, const std::string& error) {
    logError(ErrorLevel::CRITICAL, ErrorCategory::SYSTEM, source, error);
    
    // 关键错误可能需要系统重启
    Serial.printf("[ErrorHandler] 关键错误: %s - %s\n", source.c_str(), error.c_str());
}

void ErrorHandler::handleSystemRestart(const std::string& reason) {
    logCritical("System", "系统重启: " + reason);
    
    // 保存错误历史到Flash
    // TODO: 实现错误历史持久化
    
    delay(1000);
    ESP.restart();
}

std::vector<ErrorRecord> ErrorHandler::getRecentErrors(size_t count) {
    std::vector<ErrorRecord> result;
    
    if (lockMutex()) {
        size_t startIndex = errorHistory.size() > count ? errorHistory.size() - count : 0;
        for (size_t i = startIndex; i < errorHistory.size(); i++) {
            result.push_back(errorHistory[i]);
        }
        unlockMutex();
    }
    
    return result;
}

std::vector<ErrorRecord> ErrorHandler::getErrorsByLevel(ErrorLevel level) {
    std::vector<ErrorRecord> result;
    
    if (lockMutex()) {
        for (const auto& error : errorHistory) {
            if (error.level == level) {
                result.push_back(error);
            }
        }
        unlockMutex();
    }
    
    return result;
}

std::vector<ErrorRecord> ErrorHandler::getErrorsByCategory(ErrorCategory category) {
    std::vector<ErrorRecord> result;
    
    if (lockMutex()) {
        for (const auto& error : errorHistory) {
            if (error.category == category) {
                result.push_back(error);
            }
        }
        unlockMutex();
    }
    
    return result;
}

ErrorRecord ErrorHandler::getLastError() {
    ErrorRecord result;
    
    if (lockMutex()) {
        if (!errorHistory.empty()) {
            result = errorHistory.back();
        }
        unlockMutex();
    }
    
    return result;
}

uint32_t ErrorHandler::getErrorCount(ErrorLevel level) {
    int index = static_cast<int>(level);
    return (index >= 0 && index < errorCountByLevel.size()) ? errorCountByLevel[index] : 0;
}

uint32_t ErrorHandler::getErrorCount(ErrorCategory category) {
    int index = static_cast<int>(category);
    return (index >= 0 && index < errorCountByCategory.size()) ? errorCountByCategory[index] : 0;
}

JsonDocument ErrorHandler::getErrorStatistics() {
    JsonDocument doc;
    
    doc["total_errors"] = totalErrors;
    doc["critical_errors"] = criticalErrors;
    doc["queue_size"] = errorQueue.size();
    doc["history_size"] = errorHistory.size();
    
    // 按级别统计
    JsonArray levelStats = doc["by_level"].to<JsonArray>();
    for (int i = 0; i < errorCountByLevel.size(); i++) {
        JsonObject levelStat = levelStats.add<JsonObject>();
        levelStat["level"] = levelToString(static_cast<ErrorLevel>(i));
        levelStat["count"] = errorCountByLevel[i];
    }
    
    // 按类别统计
    JsonArray categoryStats = doc["by_category"].to<JsonArray>();
    for (int i = 0; i < errorCountByCategory.size(); i++) {
        JsonObject categoryStat = categoryStats.add<JsonObject>();
        categoryStat["category"] = categoryToString(static_cast<ErrorCategory>(i));
        categoryStat["count"] = errorCountByCategory[i];
    }
    
    return doc;
}

void ErrorHandler::clearErrorHistory() {
    if (lockMutex()) {
        errorHistory.clear();
        unlockMutex();
    }
}

void ErrorHandler::clearErrorQueue() {
    if (lockMutex()) {
        while (!errorQueue.empty()) {
            errorQueue.pop();
        }
        unlockMutex();
    }
}

void ErrorHandler::clearOldErrors(uint32_t olderThanMs) {
    if (lockMutex()) {
        uint64_t cutoffTime = millis() - olderThanMs;
        
        errorHistory.erase(
            std::remove_if(errorHistory.begin(), errorHistory.end(),
                [cutoffTime](const ErrorRecord& error) {
                    return error.timestamp < cutoffTime;
                }),
            errorHistory.end()
        );
        
        unlockMutex();
    }
}

void ErrorHandler::processErrorQueue() {
    if (!lockMutex()) return;
    
    // 处理队列中的错误
    while (!errorQueue.empty()) {
        ErrorRecord error = errorQueue.front();
        errorQueue.pop();
        
        // 添加到历史记录
        addErrorToHistory(error);
        
        // 广播错误事件
        broadcastError(error);
    }
    
    unlockMutex();
}

void ErrorHandler::addErrorToHistory(const ErrorRecord& error) {
    if (errorHistory.size() >= maxHistorySize) {
        errorHistory.erase(errorHistory.begin());
    }
    errorHistory.push_back(error);
}

void ErrorHandler::broadcastError(const ErrorRecord& error) {
    if (!enableWebSocketBroadcast) return;

    // 创建WebSocket错误事件数据 (匹配前端error事件格式)
    JsonDocument eventData = createErrorEventData(error);

    // 发送到WebSocket (通过事件系统)
    sendErrorToWebSocket(error);
}

void ErrorHandler::outputToSerial(const ErrorRecord& error) {
    String formattedMessage = formatErrorMessage(error).c_str();
    Serial.println(formattedMessage);
}

std::string ErrorHandler::levelToString(ErrorLevel level) {
    switch (level) {
        case ErrorLevel::DEBUG: return "DEBUG";
        case ErrorLevel::INFO: return "INFO";
        case ErrorLevel::WARNING: return "WARNING";
        case ErrorLevel::ERROR: return "ERROR";
        case ErrorLevel::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

std::string ErrorHandler::categoryToString(ErrorCategory category) {
    switch (category) {
        case ErrorCategory::SYSTEM: return "SYSTEM";
        case ErrorCategory::NETWORK: return "NETWORK";
        case ErrorCategory::HARDWARE: return "HARDWARE";
        case ErrorCategory::STORAGE: return "STORAGE";
        case ErrorCategory::API: return "API";
        case ErrorCategory::WEBSOCKET: return "WEBSOCKET";
        case ErrorCategory::SERVICE: return "SERVICE";
        case ErrorCategory::USER: return "USER";
        default: return "UNKNOWN";
    }
}

std::string ErrorHandler::formatErrorMessage(const ErrorRecord& error) {
    char buffer[512];
    snprintf(buffer, sizeof(buffer),
             "[%s][%s][Core%d] %s: %s %s",
             levelToString(error.level).c_str(),
             categoryToString(error.category).c_str(),
             error.coreId,
             error.source.c_str(),
             error.message.c_str(),
             error.details.empty() ? "" : ("- " + error.details).c_str());

    return std::string(buffer);
}

bool ErrorHandler::lockMutex(uint32_t timeoutMs) {
    if (errorMutex == nullptr) return false;
    return xSemaphoreTake(errorMutex, pdMS_TO_TICKS(timeoutMs)) == pdTRUE;
}

void ErrorHandler::unlockMutex() {
    if (errorMutex != nullptr) {
        xSemaphoreGive(errorMutex);
    }
}

void ErrorHandler::cleanupOldErrors() {
    // 清理1小时前的错误
    clearOldErrors(3600000);

    // 优化存储
    optimizeStorage();
}

void ErrorHandler::optimizeStorage() {
    if (lockMutex()) {
        // 如果历史记录过多，保留最新的一半
        if (errorHistory.size() > maxHistorySize) {
            size_t keepCount = maxHistorySize / 2;
            errorHistory.erase(errorHistory.begin(),
                             errorHistory.end() - keepCount);
        }
        unlockMutex();
    }
}

JsonDocument ErrorHandler::createErrorEventData(const ErrorRecord& error) {
    JsonDocument doc;

    // 匹配前端error事件格式
    doc["type"] = "error";
    doc["payload"]["code"] = "ERR_" + std::to_string(error.id);
    doc["payload"]["message"] = error.message;
    doc["payload"]["severity"] = levelToString(error.level);
    doc["payload"]["source"] = error.source;
    doc["payload"]["category"] = categoryToString(error.category);
    doc["payload"]["details"] = error.details;
    doc["payload"]["coreId"] = error.coreId;
    doc["payload"]["freeHeap"] = error.freeHeap;
    doc["timestamp"] = error.timestamp;

    return doc;
}

void ErrorHandler::sendErrorToWebSocket(const ErrorRecord& error) {
    // 通过事件系统发送到WebSocket
    // 这里需要与EventManager配合

    // 创建错误事件
    if (g_eventManager) {
        JsonDocument errorData = createErrorEventData(error);
        g_eventManager->emit(EventType::ERROR_OCCURRED, errorData);
    }
}

/**
 * @file DualCoreManager.cpp
 * @brief 双核管理器实现 - ESP32-S3双核并行架构
 * 
 * 实现双核任务管理、核间通信、服务协调
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "DualCoreManager.h"
#include "EventManager.h"
#include "ErrorHandler.h"
#include "../network/NetworkManager.h"
#include "../hardware/HardwareManager.h"
#include "../services/BaseService.h"

// 全局实例
DualCoreManager* g_dualCoreManager = nullptr;

DualCoreManager::DualCoreManager() 
    : core0TaskHandle(nullptr), core1TaskHandle(nullptr),
      core0ToCore1Queue(nullptr), core1ToCore0Queue(nullptr),
      interCoreMutex(nullptr), systemInitialized(false),
      core0Ready(false), core1Ready(false), systemStartTime(0),
      lastHeartbeat(0), core0TaskCount(0), core1TaskCount(0),
      interCoreMessageCount(0) {
    
    // 设置全局实例
    g_dualCoreManager = this;
}

DualCoreManager::~DualCoreManager() {
    stop();
    cleanupQueues();
    g_dualCoreManager = nullptr;
}

bool DualCoreManager::init() {
    Serial.println("[DualCore] 初始化双核管理器...");
    
    systemStartTime = millis();
    
    // 初始化队列和信号量
    if (!initializeQueues()) {
        Serial.println("[DualCore] 队列初始化失败");
        return false;
    }
    
    // 初始化核心组件
    eventManager = std::make_unique<EventManager>();
    if (!eventManager->init()) {
        Serial.println("[DualCore] 事件管理器初始化失败");
        return false;
    }
    
    errorHandler = std::make_unique<ErrorHandler>();
    if (!errorHandler->init()) {
        Serial.println("[DualCore] 错误处理器初始化失败");
        return false;
    }
    
    // 初始化网络管理器 (Core 0)
    networkManager = std::make_unique<NetworkManager>();
    
    // 初始化硬件管理器 (Core 1)
    hardwareManager = std::make_unique<HardwareManager>();
    
    systemInitialized = true;
    Serial.println("[DualCore] 双核管理器初始化完成");
    
    return true;
}

void DualCoreManager::start() {
    if (!systemInitialized) {
        Serial.println("[DualCore] 系统未初始化，无法启动");
        return;
    }
    
    Serial.println("[DualCore] 启动双核任务...");
    
    // 启动Core 0任务 (网络通信)
    if (!startCore0Tasks()) {
        Serial.println("[DualCore] Core 0任务启动失败");
        return;
    }
    
    // 启动Core 1任务 (硬件控制)
    if (!startCore1Tasks()) {
        Serial.println("[DualCore] Core 1任务启动失败");
        return;
    }
    
    Serial.println("[DualCore] 双核任务启动完成");
    
    // 发送系统就绪事件
    EventData readyEvent(EventType::SYSTEM_READY);
    readyEvent.generateId();
    broadcastEvent(readyEvent);
}

bool DualCoreManager::startCore0Tasks() {
    // 创建Core 0主任务 (网络通信、API处理)
    BaseType_t result = CREATE_CORE0_TASK(
        core0TaskFunction,
        "Core0_Network",
        8192,  // 8KB栈空间
        this,
        CORE0_NETWORK_PRIORITY,
        &core0TaskHandle
    );
    
    if (result != pdPASS) {
        Serial.println("[DualCore] Core 0任务创建失败");
        return false;
    }
    
    // 等待Core 0就绪
    uint32_t timeout = millis() + 5000;
    while (!core0Ready && millis() < timeout) {
        delay(10);
    }
    
    if (!core0Ready) {
        Serial.println("[DualCore] Core 0启动超时");
        return false;
    }
    
    Serial.println("[DualCore] Core 0任务启动成功");
    return true;
}

bool DualCoreManager::startCore1Tasks() {
    // 创建Core 1主任务 (硬件控制、红外处理)
    BaseType_t result = CREATE_CORE1_TASK(
        core1TaskFunction,
        "Core1_Hardware",
        8192,  // 8KB栈空间
        this,
        CORE1_IR_CONTROL_PRIORITY,
        &core1TaskHandle
    );
    
    if (result != pdPASS) {
        Serial.println("[DualCore] Core 1任务创建失败");
        return false;
    }
    
    // 等待Core 1就绪
    uint32_t timeout = millis() + 5000;
    while (!core1Ready && millis() < timeout) {
        delay(10);
    }
    
    if (!core1Ready) {
        Serial.println("[DualCore] Core 1启动超时");
        return false;
    }
    
    Serial.println("[DualCore] Core 1任务启动成功");
    return true;
}

void DualCoreManager::stop() {
    Serial.println("[DualCore] 停止双核任务...");
    
    systemInitialized = false;
    core0Ready = false;
    core1Ready = false;
    
    // 停止所有任务
    stopAllTasks();
    
    // 清理资源
    cleanupCore0();
    cleanupCore1();
    
    Serial.println("[DualCore] 双核任务已停止");
}

void DualCoreManager::stopAllTasks() {
    // 停止Core 0任务
    if (core0TaskHandle != nullptr) {
        vTaskDelete(core0TaskHandle);
        core0TaskHandle = nullptr;
    }
    
    // 停止Core 1任务
    if (core1TaskHandle != nullptr) {
        vTaskDelete(core1TaskHandle);
        core1TaskHandle = nullptr;
    }
}

// Core 0任务函数 (静态)
void DualCoreManager::core0TaskFunction(void* parameter) {
    DualCoreManager* manager = static_cast<DualCoreManager*>(parameter);
    
    Serial.printf("[Core0] 任务启动，运行在核心 %d\n", xPortGetCoreID());
    
    // 初始化Core 0
    if (!manager->initializeCore0()) {
        Serial.println("[Core0] 初始化失败");
        manager->handleCoreError(0, "Core 0初始化失败");
        vTaskDelete(nullptr);
        return;
    }
    
    manager->core0Ready = true;
    Serial.println("[Core0] 初始化完成，进入主循环");
    
    // Core 0主循环
    while (manager->systemInitialized) {
        manager->core0Loop();
        vTaskDelay(pdMS_TO_TICKS(10)); // 10ms延迟
    }
    
    Serial.println("[Core0] 任务退出");
    vTaskDelete(nullptr);
}

// Core 1任务函数 (静态)
void DualCoreManager::core1TaskFunction(void* parameter) {
    DualCoreManager* manager = static_cast<DualCoreManager*>(parameter);
    
    Serial.printf("[Core1] 任务启动，运行在核心 %d\n", xPortGetCoreID());
    
    // 初始化Core 1
    if (!manager->initializeCore1()) {
        Serial.println("[Core1] 初始化失败");
        manager->handleCoreError(1, "Core 1初始化失败");
        vTaskDelete(nullptr);
        return;
    }
    
    manager->core1Ready = true;
    Serial.println("[Core1] 初始化完成，进入主循环");
    
    // Core 1主循环
    while (manager->systemInitialized) {
        manager->core1Loop();
        vTaskDelay(pdMS_TO_TICKS(5)); // 5ms延迟，更高频率处理硬件
    }
    
    Serial.println("[Core1] 任务退出");
    vTaskDelete(nullptr);
}

// Core 0主循环 (网络通信)
void DualCoreManager::core0Loop() {
    core0TaskCount++;
    
    // 处理网络管理器
    if (networkManager) {
        networkManager->loop();
    }
    
    // 处理Core 0服务
    for (auto& service : core0Services) {
        if (service && service->isRunning()) {
            service->loop();
        }
    }
    
    // 处理来自Core 1的消息
    processCore0Messages();
    
    // 定期发送心跳
    if (millis() - lastHeartbeat > 1000) {
        sendHeartbeat();
        lastHeartbeat = millis();
    }
}

// Core 1主循环 (硬件控制)
void DualCoreManager::core1Loop() {
    core1TaskCount++;
    
    // 处理硬件管理器
    if (hardwareManager) {
        hardwareManager->loop();
    }
    
    // 处理Core 1服务
    for (auto& service : core1Services) {
        if (service && service->isRunning()) {
            service->loop();
        }
    }
    
    // 处理来自Core 0的消息
    processCore1Messages();
}

bool DualCoreManager::initializeQueues() {
    // 创建双核间通信队列
    core0ToCore1Queue = xQueueCreate(INTER_CORE_QUEUE_SIZE, sizeof(EventData));
    if (core0ToCore1Queue == nullptr) {
        return false;
    }
    
    core1ToCore0Queue = xQueueCreate(INTER_CORE_QUEUE_SIZE, sizeof(EventData));
    if (core1ToCore0Queue == nullptr) {
        return false;
    }
    
    // 创建互斥信号量
    interCoreMutex = xSemaphoreCreateMutex();
    if (interCoreMutex == nullptr) {
        return false;
    }
    
    return true;
}

bool DualCoreManager::initializeCore0() {
    // 初始化网络管理器
    if (networkManager && !networkManager->init()) {
        return false;
    }
    
    // 初始化Core 0服务
    for (auto& service : core0Services) {
        if (service && !service->init()) {
            return false;
        }
    }
    
    return true;
}

bool DualCoreManager::initializeCore1() {
    // 初始化硬件管理器
    if (hardwareManager && !hardwareManager->init()) {
        return false;
    }
    
    // 初始化Core 1服务
    for (auto& service : core1Services) {
        if (service && !service->init()) {
            return false;
        }
    }
    
    return true;
}

void DualCoreManager::cleanupQueues() {
    if (core0ToCore1Queue) {
        vQueueDelete(core0ToCore1Queue);
        core0ToCore1Queue = nullptr;
    }
    
    if (core1ToCore0Queue) {
        vQueueDelete(core1ToCore0Queue);
        core1ToCore0Queue = nullptr;
    }
    
    if (interCoreMutex) {
        vSemaphoreDelete(interCoreMutex);
        interCoreMutex = nullptr;
    }
}

// 双核间通信实现
bool DualCoreManager::sendToCore0(const EventData& event) {
    if (core1ToCore0Queue == nullptr) {
        return false;
    }

    EventData eventCopy = event;
    eventCopy.setTargetCore(0);
    eventCopy.setSourceCore(1);

    BaseType_t result = xQueueSend(core1ToCore0Queue, &eventCopy, pdMS_TO_TICKS(100));
    if (result == pdPASS) {
        interCoreMessageCount++;
        return true;
    }

    return false;
}

bool DualCoreManager::sendToCore1(const EventData& event) {
    if (core0ToCore1Queue == nullptr) {
        return false;
    }

    EventData eventCopy = event;
    eventCopy.setTargetCore(1);
    eventCopy.setSourceCore(0);

    BaseType_t result = xQueueSend(core0ToCore1Queue, &eventCopy, pdMS_TO_TICKS(100));
    if (result == pdPASS) {
        interCoreMessageCount++;
        return true;
    }

    return false;
}

bool DualCoreManager::broadcastEvent(const EventData& event) {
    bool success = true;

    // 发送到本地事件管理器
    if (eventManager) {
        eventManager->emit(event);
    }

    // 发送到另一个核心
    if (IS_CORE0()) {
        success &= sendToCore1(event);
    } else {
        success &= sendToCore0(event);
    }

    return success;
}

void DualCoreManager::processCore0Messages() {
    EventData event;

    // 处理来自Core 1的消息
    while (xQueueReceive(core1ToCore0Queue, &event, 0) == pdPASS) {
        handleInterCoreEvent(event);
    }
}

void DualCoreManager::processCore1Messages() {
    EventData event;

    // 处理来自Core 0的消息
    while (xQueueReceive(core0ToCore1Queue, &event, 0) == pdPASS) {
        handleInterCoreEvent(event);
    }
}

void DualCoreManager::handleInterCoreEvent(const EventData& event) {
    // 转发到本地事件管理器
    if (eventManager) {
        eventManager->emit(event);
    }

    // 记录核间通信统计
    interCoreMessageCount++;

    // 特殊事件处理
    switch (event.type) {
        case EventType::CORE_SYNC_REQUEST:
            // 处理核心同步请求
            break;

        case EventType::SYSTEM_ERROR:
            // 处理系统错误
            if (errorHandler) {
                errorHandler->handleError("InterCore", "收到系统错误事件");
            }
            break;

        case EventType::HEARTBEAT:
            // 更新心跳时间
            lastHeartbeat = millis();
            break;

        default:
            // 其他事件直接转发
            break;
    }
}

void DualCoreManager::sendHeartbeat() {
    EventData heartbeat(EventType::HEARTBEAT);
    heartbeat.generateId();
    heartbeat.data["core"] = CURRENT_CORE();
    heartbeat.data["uptime"] = getUptime();
    heartbeat.data["task_count"] = IS_CORE0() ? core0TaskCount : core1TaskCount;
    heartbeat.data["free_heap"] = ESP.getFreeHeap();

    // 发送到另一个核心
    if (IS_CORE0()) {
        sendToCore1(heartbeat);
    } else {
        sendToCore0(heartbeat);
    }
}

void DualCoreManager::handleCoreError(uint8_t coreId, const std::string& error) {
    if (errorHandler) {
        std::string errorMsg = "Core " + std::to_string(coreId) + " 错误: " + error;
        errorHandler->handleError("DualCore", errorMsg);
    }

    // 发送错误事件
    EventData errorEvent(EventType::SYSTEM_ERROR);
    errorEvent.generateId();
    errorEvent.data["core"] = coreId;
    errorEvent.data["error"] = error;
    errorEvent.data["timestamp"] = millis();

    broadcastEvent(errorEvent);
}

void DualCoreManager::cleanupCore0() {
    // 清理Core 0服务
    for (auto& service : core0Services) {
        if (service) {
            service->cleanup();
        }
    }
    core0Services.clear();

    // 清理网络管理器
    if (networkManager) {
        networkManager->cleanup();
    }
}

void DualCoreManager::cleanupCore1() {
    // 清理Core 1服务
    for (auto& service : core1Services) {
        if (service) {
            service->cleanup();
        }
    }
    core1Services.clear();

    // 清理硬件管理器
    if (hardwareManager) {
        hardwareManager->cleanup();
    }
}

void DualCoreManager::restart() {
    Serial.println("[DualCore] 重启系统...");

    stop();
    delay(1000);

    if (init()) {
        start();
        Serial.println("[DualCore] 系统重启完成");
    } else {
        Serial.println("[DualCore] 系统重启失败");
    }
}

/**
 * @file OTAService.h
 * @brief OTA升级服务 - 固件无线升级
 * 
 * 支持Web OTA和Arduino OTA升级
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef OTA_SERVICE_H
#define OTA_SERVICE_H

#include "BaseService.h"

class OTAService : public BaseService {
private:
    bool otaEnabled;
    bool otaInProgress;

public:
    OTAService(EventManager* em);
    virtual ~OTAService() = default;

    // BaseService接口实现
    bool init() override;
    void loop() override;
    void cleanup() override;

    // OTA控制
    bool startOTA();
    void stopOTA();
    bool isOTAInProgress() const { return otaInProgress; }
};

#endif

/**
 * @file NetworkConfig.h
 * @brief 网络配置常量定义
 * 
 * WiFi和网络服务配置常量
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef NETWORK_CONFIG_H
#define NETWORK_CONFIG_H

#include <Arduino.h>

// 默认网络配置
#define DEFAULT_AP_SSID         "ESP32_IR_Controller"
#define DEFAULT_AP_PASSWORD     "12345678"
#define DEFAULT_AP_CHANNEL      1
#define DEFAULT_AP_MAX_CONN     4

// 服务器端口配置
#define DEFAULT_HTTP_PORT       80
#define DEFAULT_WEBSOCKET_PORT  81
#define DEFAULT_OTA_PORT        3232

// 网络超时配置
#define WIFI_CONNECT_TIMEOUT    30000   // 30秒
#define HTTP_REQUEST_TIMEOUT    5000    // 5秒
#define WEBSOCKET_PING_INTERVAL 30000   // 30秒

// 连接限制
#define MAX_WEBSOCKET_CLIENTS   10
#define MAX_HTTP_CONNECTIONS    5

// IP配置
#define DEFAULT_STATIC_IP       "*************"
#define DEFAULT_GATEWAY         "***********"
#define DEFAULT_SUBNET          "*************"
#define DEFAULT_DNS1            "*******"
#define DEFAULT_DNS2            "*******"

#endif

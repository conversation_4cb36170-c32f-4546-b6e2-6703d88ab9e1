/**
 * @file APIRouter.h
 * @brief API路由器 - 完整实现8个HTTP API
 * 
 * 基于前端完整数据文档的API需求设计
 * 完整实现：status, signals, learning, emit/signal, signals/{id}, signals/clear, batch
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef API_ROUTER_H
#define API_ROUTER_H

#include "../services/BaseService.h"
#include "../types/APITypes.h"
#include "../types/SignalData.h"
#include <ESPAsyncWebServer.h>
#include <functional>
#include <unordered_map>

// 前向声明
class SignalService;
class IRControlService;
class StatusService;
class ConfigService;

// API处理器类型
using APIHandler = std::function<void(AsyncWebServerRequest*)>;

class APIRouter : public BaseService {
private:
    AsyncWebServer* server;
    
    // 服务引用
    SignalService* signalService;
    IRControlService* controlService;
    StatusService* statusService;
    ConfigService* configService;
    
    // 路由映射
    std::unordered_map<std::string, APIHandler> getRoutes;
    std::unordered_map<std::string, APIHandler> postRoutes;
    std::unordered_map<std::string, APIHandler> putRoutes;
    std::unordered_map<std::string, APIHandler> deleteRoutes;
    
    // 统计信息
    uint32_t totalAPIRequests;
    uint32_t totalAPIErrors;

public:
    APIRouter(EventManager* em, AsyncWebServer* srv);
    virtual ~APIRouter() = default;

    // BaseService接口实现
    bool init() override;
    void loop() override;
    void cleanup() override;

    // 路由设置
    void setupRoutes();
    void setServiceReferences(SignalService* signals, IRControlService* control,
                             StatusService* status, ConfigService* config);

private:
    // ========== 完整实现8个HTTP API (匹配前端需求) ==========
    
    // 1. GET /api/status - 系统状态查询
    void handleGetStatus(AsyncWebServerRequest* request);
    
    // 2. GET /api/signals - 获取信号列表
    void handleGetSignals(AsyncWebServerRequest* request);
    
    // 3. POST /api/learning - 信号学习控制 (start/stop)
    void handlePostLearning(AsyncWebServerRequest* request);
    
    // 4. POST /api/emit/signal - 信号发射
    void handlePostEmitSignal(AsyncWebServerRequest* request);
    
    // 5. PUT /api/signals/{id} - 更新信号
    void handlePutSignal(AsyncWebServerRequest* request);
    
    // 6. DELETE /api/signals/{id} - 删除信号
    void handleDeleteSignal(AsyncWebServerRequest* request);
    
    // 7. POST /api/signals/clear - 清空所有信号
    void handlePostClearSignals(AsyncWebServerRequest* request);
    
    // 8. POST /api/batch - 批量请求处理
    void handlePostBatch(AsyncWebServerRequest* request);
    
    // ========== 辅助API处理方法 ==========
    
    // 请求解析
    JsonDocument parseRequestBody(AsyncWebServerRequest* request);
    std::string getPathParameter(AsyncWebServerRequest* request, const std::string& param);
    std::string getQueryParameter(AsyncWebServerRequest* request, const std::string& param);
    
    // 响应生成
    void sendAPIResponse(AsyncWebServerRequest* request, const APIResponse& response);
    void sendSuccessResponse(AsyncWebServerRequest* request, const JsonVariant& data = JsonVariant(), 
                           const std::string& message = "操作成功");
    void sendErrorResponse(AsyncWebServerRequest* request, const std::string& error, 
                          const std::string& message = "", int httpCode = 400);
    
    // 批量请求处理
    void processBatchRequest(const std::vector<BatchRequest>& requests, 
                           std::vector<BatchResponse>& responses);
    BatchResponse processSingleBatchRequest(const BatchRequest& request);
    
    // 验证和安全
    bool validateRequest(AsyncWebServerRequest* request);
    bool validateSignalData(const JsonObject& signalJson);
    bool validateLearningRequest(const JsonObject& requestJson);
    bool validateEmitRequest(const JsonObject& requestJson);
    
    // 错误处理
    void handleAPIError(AsyncWebServerRequest* request, const std::string& operation, 
                       const std::string& error);
    void logAPIRequest(AsyncWebServerRequest* request);
    void logAPIResponse(AsyncWebServerRequest* request, bool success);
    
    // 统计更新
    void incrementRequestCount();
    void incrementErrorCount();
    
    // CORS处理
    void addCORSHeaders(AsyncWebServerResponse* response);
    void handleOptionsRequest(AsyncWebServerRequest* request);
    
    // 路由注册辅助
    void registerGETRoute(const std::string& path, APIHandler handler);
    void registerPOSTRoute(const std::string& path, APIHandler handler);
    void registerPUTRoute(const std::string& path, APIHandler handler);
    void registerDELETERoute(const std::string& path, APIHandler handler);
};

#endif

/**
 * @file StatusLED.h
 * @brief 状态LED控制器 - 系统状态指示
 * 
 * 基于前端完整数据文档的状态指示需求
 * 支持多种LED模式、PWM控制、状态指示
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef STATUS_LED_H
#define STATUS_LED_H

#include <Arduino.h>
#include "../core/EventManager.h"

// LED模式枚举
enum class LEDMode {
    OFF,                // 关闭
    ON,                 // 常亮
    SLOW_BLINK,         // 慢闪 (1Hz)
    FAST_BLINK,         // 快闪 (5Hz)
    PULSE,              // 呼吸灯
    LEARNING,           // 学习模式 (快速双闪)
    TRANSMITTING,       // 发射模式 (快闪)
    ERROR,              // 错误模式 (SOS闪烁)
    WIFI_CONNECTING,    // WiFi连接中 (慢闪)
    WIFI_CONNECTED,     // WiFi已连接 (常亮)
    CUSTOM              // 自定义模式
};

// LED状态
struct LEDState {
    LEDMode mode;
    uint8_t brightness;     // 0-255
    uint16_t period;        // 周期(ms)
    uint16_t onTime;        // 亮时间(ms)
    uint16_t offTime;       // 灭时间(ms)
    bool inverted;          // 是否反相
    
    LEDState() : mode(LEDMode::OFF), brightness(255), period(1000), 
                 onTime(500), offTime(500), inverted(false) {}
};

class StatusLED {
private:
    uint8_t ledPin;
    uint8_t pwmChannel;
    uint32_t pwmFrequency;
    uint8_t pwmResolution;
    EventManager* eventManager;
    
    // LED状态
    LEDState currentState;
    LEDMode currentMode;
    uint32_t lastUpdate;
    uint32_t cycleStart;
    bool ledOn;
    
    // 自定义模式
    std::vector<uint16_t> customPattern;
    size_t patternIndex;
    
    // 配置
    bool enabled;
    uint8_t maxBrightness;

public:
    StatusLED(EventManager* em);
    ~StatusLED();

    // 生命周期
    bool init(uint8_t pin, uint8_t channel = 0, bool inverted = false);
    void cleanup();
    void loop();

    // LED控制接口
    void setMode(LEDMode mode);
    void setBrightness(uint8_t brightness);
    void setEnabled(bool enable);
    void turnOn();
    void turnOff();
    
    // 自定义模式
    void setCustomPattern(const std::vector<uint16_t>& pattern);
    void setCustomMode(uint16_t onTime, uint16_t offTime, uint8_t brightness = 255);
    
    // 状态查询
    LEDMode getMode() const { return currentMode; }
    uint8_t getBrightness() const { return currentState.brightness; }
    bool isEnabled() const { return enabled; }
    bool isOn() const { return ledOn; }
    
    // 系统状态指示 (匹配前端状态)
    void showSystemReady();
    void showWiFiConnecting();
    void showWiFiConnected();
    void showWiFiDisconnected();
    void showLearningMode();
    void showTransmitting();
    void showError();
    void showIdle();

private:
    // LED控制
    void updateLED();
    void setLEDState(bool on);
    void setLEDPWM(uint8_t value);
    
    // 模式处理
    void processMode();
    void processBlinkMode();
    void processPulseMode();
    void processCustomMode();
    
    // 事件处理
    void subscribeToSystemEvents();
    void handleSystemEvent(const EventData& event);
    void onSystemStateChanged(const std::string& state);
    void onNetworkStateChanged(const std::string& state);
    void onLearningStateChanged(const std::string& state);
    void onTransmitStateChanged(const std::string& state);
    
    // 辅助方法
    uint8_t calculatePulseValue(uint32_t time, uint16_t period);
    void resetCycle();
    bool isTimeForUpdate();
    
    // 硬件控制
    bool initializePWM();
    void configurePWM();
    void cleanupPWM();
};

#endif

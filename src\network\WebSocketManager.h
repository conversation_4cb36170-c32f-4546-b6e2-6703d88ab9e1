/**
 * @file WebSocketManager.h
 * @brief WebSocket管理器 - 实时通信服务
 * 
 * 基于前端完整数据文档的WebSocket事件需求
 * 支持6个WebSocket事件：connected, disconnected, signal_learned, signal_sent, status_update, error
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef WEBSOCKET_MANAGER_H
#define WEBSOCKET_MANAGER_H

#include "../services/BaseService.h"
#include "../types/SignalData.h"
#include "../types/APITypes.h"
#include <ESPAsyncWebServer.h>
#include <vector>
#include <memory>

// WebSocket客户端信息
struct WebSocketClient {
    uint32_t id;
    std::string clientId;
    uint64_t connectedTime;
    uint64_t lastPing;
    bool isActive;
    
    WebSocketClient() : id(0), connectedTime(0), lastPing(0), isActive(false) {}
};

class WebSocketManager : public BaseService {
private:
    std::unique_ptr<AsyncWebSocket> webSocket;
    std::vector<WebSocketClient> clients;
    uint32_t nextClientId;
    
    // 配置
    uint16_t websocketPort;
    uint32_t pingInterval;
    uint32_t clientTimeout;
    
    // 统计信息
    uint32_t totalConnections;
    uint32_t totalDisconnections;
    uint32_t totalMessagesSent;
    uint32_t totalMessagesReceived;

public:
    WebSocketManager(EventManager* em);
    virtual ~WebSocketManager();

    // BaseService接口实现
    bool init() override;
    void loop() override;
    void cleanup() override;

    // WebSocket控制
    bool startWebSocket(AsyncWebServer* server, uint16_t port = 81);
    void stopWebSocket();
    
    // 客户端管理
    size_t getClientCount() const { return clients.size(); }
    std::vector<WebSocketClient> getActiveClients();
    void disconnectClient(uint32_t clientId);
    void disconnectAllClients();
    
    // 消息广播 - 完整实现6个WebSocket事件 (匹配前端需求)
    void broadcastConnected(uint32_t clientId, const std::string& clientInfo = "");
    void broadcastDisconnected(uint32_t clientId, const std::string& reason = "");
    void broadcastSignalLearned(const SignalData& signal, uint8_t quality = 100, uint32_t learningTime = 0);
    void broadcastSignalSent(const std::string& signalId, bool success = true, uint32_t duration = 0);
    void broadcastStatusUpdate(const SystemStatus& status);
    void broadcastError(const std::string& code, const std::string& message, const std::string& severity = "error");
    
    // 通用消息发送
    void broadcastMessage(const WebSocketMessage& message);
    void sendToClient(uint32_t clientId, const WebSocketMessage& message);
    void sendToClient(uint32_t clientId, const std::string& message);
    
    // 统计信息
    uint32_t getTotalConnections() const { return totalConnections; }
    uint32_t getTotalDisconnections() const { return totalDisconnections; }
    uint32_t getTotalMessagesSent() const { return totalMessagesSent; }
    uint32_t getTotalMessagesReceived() const { return totalMessagesReceived; }

private:
    // WebSocket事件处理
    void onEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                AwsEventType type, void* arg, uint8_t* data, size_t len);
    void onConnect(AsyncWebSocketClient* client);
    void onDisconnect(AsyncWebSocketClient* client);
    void onMessage(AsyncWebSocketClient* client, uint8_t* data, size_t len);
    void onError(AsyncWebSocketClient* client, const std::string& error);
    
    // 客户端管理
    void addClient(AsyncWebSocketClient* client);
    void removeClient(uint32_t clientId);
    WebSocketClient* findClient(uint32_t clientId);
    void updateClientActivity(uint32_t clientId);
    
    // 心跳和清理
    void pingClients();
    void cleanupInactiveClients();
    
    // 消息处理
    void processIncomingMessage(AsyncWebSocketClient* client, const std::string& message);
    void handleClientMessage(uint32_t clientId, const JsonObject& message);
    
    // 事件创建辅助 (匹配前端WebSocket事件格式)
    WebSocketMessage createConnectedEvent(uint32_t clientId, const std::string& clientInfo);
    WebSocketMessage createDisconnectedEvent(uint32_t clientId, const std::string& reason);
    WebSocketMessage createSignalLearnedEvent(const SignalData& signal, uint8_t quality, uint32_t learningTime);
    WebSocketMessage createSignalSentEvent(const std::string& signalId, bool success, uint32_t duration);
    WebSocketMessage createStatusUpdateEvent(const SystemStatus& status);
    WebSocketMessage createErrorEvent(const std::string& code, const std::string& message, const std::string& severity);
    
    // 辅助方法
    std::string generateClientId();
    void logWebSocketActivity(const std::string& activity, uint32_t clientId = 0);
    void updateStatistics();
    
    // 事件订阅 (监听系统事件并转发到WebSocket)
    void subscribeToSystemEvents();
    void handleSystemEvent(const EventData& event);
};

#endif

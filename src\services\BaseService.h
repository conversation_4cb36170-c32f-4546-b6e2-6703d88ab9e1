/**
 * @file BaseService.h
 * @brief 服务基类 - 双核架构服务框架
 * 
 * 基于前端完整数据文档的服务需求设计
 * 支持双核服务管理、事件通信、错误处理
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef BASE_SERVICE_H
#define BASE_SERVICE_H

#include <Arduino.h>
#include <string>
#include "../core/EventManager.h"
#include "../core/ErrorHandler.h"
#include "../types/EventTypes.h"

// 服务状态枚举
enum class ServiceStatus {
    STOPPED,
    STARTING,
    RUNNING,
    ERROR,
    STOPPING
};

// 服务类型枚举
enum class ServiceType {
    CORE0_SERVICE,      // Core 0服务 (网络通信)
    CORE1_SERVICE,      // Core 1服务 (硬件控制)
    SHARED_SERVICE      // 共享服务 (可在任意核心运行)
};

class BaseService {
protected:
    EventManager* eventManager;
    ErrorHandler* errorHandler;
    std::string serviceName;
    ServiceStatus serviceStatus;
    ServiceType serviceType;
    bool serviceInitialized;
    uint8_t targetCore;         // 目标运行核心
    uint32_t serviceStartTime;
    uint32_t lastLoopTime;
    uint32_t loopCount;

public:
    BaseService(EventManager* em, const std::string& name, 
                ServiceType type = ServiceType::SHARED_SERVICE);
    virtual ~BaseService() = default;

    // 纯虚函数 - 子类必须实现
    virtual bool init() = 0;
    virtual void loop() = 0;
    virtual void cleanup() = 0;

    // 可选重写的虚函数
    virtual void onConfigChanged() {}
    virtual void onSystemRestart() {}
    virtual void onCoreSwitch(uint8_t newCore) {}

    // 状态管理
    ServiceStatus getStatus() const { return serviceStatus; }
    ServiceType getType() const { return serviceType; }
    bool isRunning() const { return serviceStatus == ServiceStatus::RUNNING; }
    bool isInitialized() const { return serviceInitialized; }
    const std::string& getName() const { return serviceName; }
    uint8_t getTargetCore() const { return targetCore; }

    // 性能统计
    uint32_t getUptime() const;
    uint32_t getLoopCount() const { return loopCount; }
    uint32_t getLastLoopTime() const { return lastLoopTime; }
    float getLoopFrequency() const;

    // 错误处理
    void logError(const std::string& operation, const std::string& error);
    void logWarning(const std::string& operation, const std::string& warning);
    void logInfo(const std::string& message);
    void logDebug(const std::string& message);

    // 事件发布
    void emitEvent(EventType type, const JsonDocument& data = JsonDocument());
    void emitServiceEvent(const std::string& event, const JsonDocument& data = JsonDocument());

    // 服务间通信
    void sendToCore0(EventType type, const JsonDocument& data = JsonDocument());
    void sendToCore1(EventType type, const JsonDocument& data = JsonDocument());
    void broadcastEvent(EventType type, const JsonDocument& data = JsonDocument());

protected:
    // 状态管理
    void setStatus(ServiceStatus status);
    void setInitialized(bool initialized);
    void setTargetCore(uint8_t core);

    // 生命周期辅助
    bool startService();
    void stopService();
    void restartService();

    // 事件订阅辅助
    uint32_t subscribeEvent(EventType type, EventHandler handler);
    uint32_t subscribeEventOnce(EventType type, EventHandler handler);
    void unsubscribeEvent(EventType type, uint32_t subscriptionId);

    // 配置访问
    template<typename T>
    T getConfig(const std::string& key, const T& defaultValue);
    template<typename T>
    bool setConfig(const std::string& key, const T& value);

    // 性能监控
    void updateLoopStats();
    void resetStats();

private:
    // 内部状态管理
    void handleStatusChange(ServiceStatus oldStatus, ServiceStatus newStatus);
    void notifyStatusChange();

    // 错误恢复
    void handleServiceError(const std::string& error);
    bool attemptRecovery();
};

// 服务工厂类
class ServiceFactory {
public:
    template<typename T, typename... Args>
    static std::unique_ptr<T> createService(EventManager* em, Args&&... args) {
        return std::make_unique<T>(em, std::forward<Args>(args)...);
    }
};

// 服务管理器辅助类
class ServiceManager {
private:
    std::vector<std::unique_ptr<BaseService>> services;
    EventManager* eventManager;

public:
    ServiceManager(EventManager* em) : eventManager(em) {}

    template<typename T, typename... Args>
    T* addService(Args&&... args) {
        auto service = ServiceFactory::createService<T>(eventManager, std::forward<Args>(args)...);
        T* servicePtr = service.get();
        services.push_back(std::move(service));
        return servicePtr;
    }

    template<typename T>
    T* getService() {
        for (auto& service : services) {
            if (auto* typedService = dynamic_cast<T*>(service.get())) {
                return typedService;
            }
        }
        return nullptr;
    }

    void initAllServices();
    void loopAllServices();
    void cleanupAllServices();
    void restartAllServices();

    size_t getServiceCount() const { return services.size(); }
    std::vector<BaseService*> getRunningServices();
    std::vector<BaseService*> getServicesByType(ServiceType type);
};

// 便捷宏定义
#define SERVICE_LOG_ERROR(operation, error) logError(operation, error)
#define SERVICE_LOG_WARNING(operation, warning) logWarning(operation, warning)
#define SERVICE_LOG_INFO(message) logInfo(message)
#define SERVICE_LOG_DEBUG(message) logDebug(message)

#define SERVICE_EMIT_EVENT(type, data) emitEvent(type, data)
#define SERVICE_EMIT_SERVICE_EVENT(event, data) emitServiceEvent(event, data)

#define SERVICE_SEND_TO_CORE0(type, data) sendToCore0(type, data)
#define SERVICE_SEND_TO_CORE1(type, data) sendToCore1(type, data)
#define SERVICE_BROADCAST_EVENT(type, data) broadcastEvent(type, data)

// 服务状态转换辅助
namespace ServiceUtils {
    std::string statusToString(ServiceStatus status);
    std::string typeToString(ServiceType type);
    ServiceStatus statusFromString(const std::string& str);
    ServiceType typeFromString(const std::string& str);
    
    bool isValidTransition(ServiceStatus from, ServiceStatus to);
    JsonDocument createServiceStatusEvent(const BaseService* service);
}

#endif

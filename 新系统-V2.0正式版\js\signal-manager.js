/**
 * R1 信号管理模块 - 完全重写版本
 * 严格按照R1系统架构文档和核心标准编写
 *
 * <AUTHOR> System
 * @version 3.0.0
 */

class SignalManager extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'SignalManager');

    // 核心数据 - 使用优化的存储系统
    this.signalStorage = new OptimizedSignalStorage();
    this.selectedSignals = new Set();

    // 兼容性属性 - 保持现有代码工作
    const storage = this.signalStorage;
    this.signals = {
      set: (id, signal) => storage.addSignal(signal),
      get: (id) => storage.getSignal(id),
      has: (id) => storage.getSignal(id) !== null,
      delete: (id) => storage.removeSignal(id),
      clear: () => storage.clear(),
      values: () => storage.getAllSignals(),
      keys: () => storage.getAllSignals().map(s => s.id),
      entries: () => storage.getAllSignals().map(s => [s.id, s]),
      forEach: (callback) => storage.getAllSignals().forEach(s => callback(s, s.id)),
      get size() { return storage.getStats().totalSignals; }
    };

    // 状态管理
    this.isLearning = false;
    this.currentView = 'grid'; // 'grid' | 'list'
    this.isMultiSelectMode = false;

    // 信号学习状态管理
    this.learningState = {
      isLearning: false,        // 是否在学习模式
      hasUnsavedSignal: false,  // 是否有未保存的信号
      pausedTasks: [],          // 暂停的任务列表
      currentSignalData: null,  // 当前检测到的信号数据
      learningStartTime: 0,     // 学习开始时间
      lastActivityTime: 0       // 最后活动时间
    };

    // 超时控制
    this.learningTimeout = null;      // 学习超时定时器
    this.autoSaveTimeout = null;      // 自动保存定时器
    this.activityCheckInterval = null; // 活动检查定时器

    // 过滤和搜索
    this.searchKeyword = '';
    this.filterType = '';
    this.sortBy = 'name';

    // 性能优化组件
    this.domUpdater = window.DOMUpdateManager;
    this.virtualList = null;

    // 统计数据
    this.stats = {
      totalSignals: 0,
      totalSent: 0,
      lastLearned: null
    };

    // 初始化页面生命周期监听
    this.initPageLifecycleListeners();
  }

  /**
   * 设置事件监听器 - 继承自BaseModule
   */
  async setupEventListeners() {
    // 监听控制模块的信号请求
    this.eventBus.on('signal.request.all', (data) => {
      this.handleSignalRequest(data);
    });

    this.eventBus.on('signal.request.by-ids', (data) => {
      this.handleSignalRequestByIds(data);
    });

    this.eventBus.on('signal.request.selected', (data) => {
      this.handleSelectedSignalRequest(data);
    });

    this.eventBus.on('signal.request.count', (data) => {
      this.handleSignalCountRequest(data);
    });

    this.eventBus.on('signal.request.send', (data) => {
      this.handleSendSignalRequest(data);
    });

    // 监听批量发射响应
    this.eventBus.on('signal.batch.emit.response', (data) => {
      this.handleBatchEmitResponse(data);
    });

    // 学习状态查询事件响应 - 符合架构标准
    this.eventBus.on('signal.learning.status.request', (data) => {
      const isLearning = this.isLearning || false;
      if (data.callback) {
        data.callback({ isLearning });
      }
    });

    // ✅ 监听控制模块的信号发射统计更新事件
    this.eventBus.on('signal.emit.success', (data) => {
      this.handleSignalEmitSuccess(data);
    });

    this.eventBus.on('signal.emit.failed', (data) => {
      this.handleSignalEmitFailed(data);
    });

    // ✅ 监听追加信号事件 - 同步选中状态
    this.eventBus.on('control.signals.appended', (data) => {
      this.handleSignalsAppended(data);
    });


  }

  /**
   * 设置UI - 继承自BaseModule
   */
  async setupUI() {
    this.initEventDelegation();
    this.initInputElements();
    this.renderInitialUI();
    this.checkForUnsavedSignalOnLoad();
    this.updateLearningUI(); // 初始化按钮状态
  }

  /**
   * 加载模块数据 - 继承自BaseModule
   */
  async loadModuleData() {
    try {
      this.loadSignalsFromStorage();
      await this.loadSignalsFromESP32();

      // 测试数据已移除 - 所有数据应来自后端

      this.updateStats();
      this.renderSignals();
    } catch (error) {
      this.handleError(error, '数据加载');
      console.warn('⚠️ 数据加载失败，请检查后端连接');
    }
  }

  /**
   * 初始化事件委托 - 使用标准addEventListener
   */
  initEventDelegation() {
    const container = $('#signal-manager');
    if (!container) {
      console.error('SignalManager: 找不到容器元素 #signal-manager');
      return;
    }

    // 使用事件委托处理所有点击事件
    container.addEventListener('click', (e) => {
      this.handleClick(e);
    });


  }

  /**
   * 初始化页面生命周期监听器
   */
  initPageLifecycleListeners() {
    // 页面卸载时自动停止学习
    window.addEventListener('beforeunload', () => {
      if (this.learningState.isLearning) {
        this.stopLearningAndCleanup();
      }
    });

    // 页面隐藏时保存状态
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.learningState.hasUnsavedSignal) {
        this.saveUnsavedSignalToLocal();
      }
    });

    // 页面获得焦点时更新活动时间
    window.addEventListener('focus', () => {
      this.updateActivityTime();
    });


  }

  /**
   * 检查页面加载时是否有未保存的信号
   */
  checkForUnsavedSignalOnLoad() {
    try {
      const unsavedSignal = localStorage.getItem('signalManager_unsavedSignal');
      if (unsavedSignal) {
        const signalData = JSON.parse(unsavedSignal);
        this.showSignalRecoveryDialog(signalData);
      }
    } catch (error) {
      console.error('SignalManager: 检查未保存信号失败:', error);
      localStorage.removeItem('signalManager_unsavedSignal');
    }
  }

  /**
   * 显示信号恢复对话框
   */
  showSignalRecoveryDialog(signalData) {
    const modalContent = `
      <div class="modal-header">
        <h3>恢复未保存的信号</h3>
      </div>
      <div class="modal-body">
        <div class="recovery-dialog">
          <p>检测到一个未完成保存的信号，是否恢复？</p>
          <div class="signal-preview">
            <strong>信号数据:</strong> ${signalData.rawData?.substring(0, 50)}...
            <br>
            <strong>检测时间:</strong> ${new Date(signalData.detectedAt).toLocaleString()}
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <div class="recovery-actions">
          <button class="btn secondary" id="discardRecoveryBtn">丢弃</button>
          <button class="btn primary" id="recoverSignalBtn">恢复编辑</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);

    // 添加事件监听器
    const discardBtn = $('#discardRecoveryBtn');
    if (discardBtn) {
      discardBtn.addEventListener('click', () => {
        this.discardRecoverySignal();
      });
    }

    const recoverBtn = $('#recoverSignalBtn');
    if (recoverBtn) {
      recoverBtn.addEventListener('click', () => {
        this.recoverUnsavedSignal();
      });
    }
  }

  /**
   * 处理点击事件 - 使用closest解决事件冒泡问题
   */
  handleClick(e) {
    try {
      const actionElement = e.target.closest('[data-action]');
      if (!actionElement) return;

      const action = actionElement.getAttribute('data-action');
      if (!action) return;

      // 获取信号ID
      let signalId = actionElement.getAttribute('data-signal-id');
      if (!signalId) {
        const signalElement = actionElement.closest('[data-signal-id]');
        signalId = signalElement?.getAttribute('data-signal-id');
      }

      // 路由到具体处理方法
      this.routeAction(action, signalId, e);

    } catch (error) {
      console.error('❌ SignalManager: 事件处理错误', error);
      this.handleError(error, '事件处理');
    }
  }

  /**
   * 动作路由 - 符合架构标准
   */
  routeAction(action, signalId, event) {
    switch (action) {
      case 'toggle-learning':
        if (this.learningState.isLearning) {
          this.stopLearning();
        } else {
          this.startLearning();
        }
        break;
      case 'toggle-view':
        this.toggleView();
        break;
      case 'toggle-multiselect':
        this.toggleMultiSelectMode();
        break;
      case 'toggle-search':
        this.toggleSearchArea();
        break;
      case 'import-signals':
        this.showImportDialog();
        break;
      case 'export-all-signals':
        this.exportAllSignals();
        break;
      case 'select-all':
        this.selectAllSignals();
        break;
      case 'select-none':
        this.selectNoneSignals();
        break;
      case 'batch-send':
        this.batchSendSignals();
        break;
      case 'export-selected':
        this.exportSelectedSignals();
        break;
      case 'delete-selected':
        this.batchDeleteSignals();
        break;
      case 'send-signal':
        if (signalId) this.sendSignal(signalId);
        break;
      case 'edit-signal':
        if (signalId) this.showEditDialog(signalId);
        break;
      case 'delete-signal':
        if (signalId) this.deleteSignal(signalId);
        break;
      case 'show-details':
        if (signalId) this.showSignalDetails(signalId);
        break;
      case 'toggle-selection':
        if (signalId) this.toggleSignalSelection(signalId);
        break;
      case 'toggle-selection-checkbox':
        event.stopPropagation();
        if (signalId) this.handleCheckboxToggle(signalId, event);
        break;

      // 增强版学习相关动作
      case 'save-learned-signal':
        this.saveLearnedSignal();
        break;

      case 'cancel-learned-signal':
        this.cancelLearnedSignal();
        break;

      case 'continue-learning':
        this.saveLearnedSignal();
        break;

      case 'recover-signal':
        this.recoverUnsavedSignal();
        break;

      case 'discard-recovery':
        this.discardRecoverySignal();
        break;

      case 'cancel-learning':
        this.stopLearning();
        break;

      default:
        console.log(`未处理的动作: ${action}`);
    }
  }

  /**
   * 初始化输入元素 - 符合架构标准
   */
  initInputElements() {
    // 搜索输入框
    const searchInput = $('#signalSearchInput');
    if (searchInput) {
      searchInput.oninput = R1Utils.debounce((e) => {
        this.searchKeyword = e.target.value;
        this.renderSignals();
      }, 300);
    }

    // 类型过滤
    const typeFilter = $('#signalTypeFilter');
    if (typeFilter) {
      typeFilter.onchange = (e) => {
        this.filterType = e.target.value;
        this.renderSignals();
      };
    }

    // 排序选择
    const sortSelect = $('#signalSortBy');
    if (sortSelect) {
      sortSelect.onchange = (e) => {
        this.sortBy = e.target.value;
        this.renderSignals();
      };
    }
  }

  /**
   * 渲染初始UI - 符合架构标准
   */
  renderInitialUI() {
    // 🔧 移除所有调试代码，符合架构文档的性能优化标准
    this.updateStats();

    // 只在首次渲染时生成HTML，避免重复渲染
    if (!this._isInitialRendered) {
      this.renderSignals();
      this._isInitialRendered = true;
    }

    this.updateViewToggleButton();
    this.updateMultiSelectButton();
  }

  /**
   * 添加测试信号数据 - 已移除，数据应来自后端
   */
  addTestSignals() {
    console.warn('⚠️ addTestSignals已被移除，信号数据应从后端获取');
    // 不再生成任何测试数据，完全依赖后端
  }

  /**
   * 从本地存储加载信号 - 已移除，信号应从后端加载
   */
  loadSignalsFromStorage() {
    console.warn('⚠️ 本地信号存储已移除，所有信号数据应从后端API获取');
    // 不再从localStorage加载业务数据
  }

  /**
   * 验证信号格式 - 确保符合标准格式
   * @param {Object} signal - 信号数据
   * @returns {boolean} - 是否符合标准格式
   */
  validateSignalFormat(signal) {
    if (!signal || typeof signal !== 'object') {
      return false;
    }

    // 检查并修复必需字段 - 宽容模式
    if (!signal.id) {
      signal.id = R1Utils.generateId('signal');
      console.log(`🔧 自动生成ID: ${signal.id}`);
    }

    if (!signal.name) {
      signal.name = '未命名信号';
      console.log(`🔧 设置默认名称: ${signal.name}`);
    }

    // 🚨 信号码是核心数据，绝对不能修改或补充！
    if (signal.signalCode === undefined || signal.signalCode === null || signal.signalCode === '') {
      console.error(`❌ 信号码缺失或为空，拒绝此信号:`, signal);
      return false; // 没有信号码的信号是无效的
    }

    // 信号码必须是字符串类型
    if (typeof signal.signalCode !== 'string') {
      console.error(`❌ 信号码类型错误(${typeof signal.signalCode})，拒绝此信号:`, signal);
      return false;
    }

    if (!signal.protocol) {
      signal.protocol = 'NEC';
      console.log(`🔧 设置默认协议: ${signal.protocol}`);
    }

    if (!signal.type) {
      signal.type = 'other';
      console.log(`🔧 设置默认类型: ${signal.type}`);
    }

    // 检查字段类型 - 宽容模式，自动转换类型
    if (signal.isLearned !== undefined) {
      if (typeof signal.isLearned === 'string') {
        // 自动转换字符串到布尔值
        signal.isLearned = signal.isLearned === 'true' || signal.isLearned === '1';
      } else if (typeof signal.isLearned !== 'boolean') {
        console.warn('⚠️ isLearned字段类型无效，设为默认值', signal);
        signal.isLearned = true;
      }
    } else {
      signal.isLearned = true; // 默认值
    }

    if (signal.sentCount !== undefined) {
      if (typeof signal.sentCount === 'string') {
        // 自动转换字符串到数字
        const parsed = parseInt(signal.sentCount);
        signal.sentCount = isNaN(parsed) ? 0 : parsed;
      } else if (typeof signal.sentCount !== 'number') {
        console.warn('⚠️ sentCount字段类型无效，设为默认值', signal);
        signal.sentCount = 0;
      }
    } else {
      signal.sentCount = 0; // 默认值
    }

    return true;
  }

  /**
   * 标准化信号数据 - 确保所有字段类型正确
   */
  normalizeSignalData(signal) {
    // 确保基本字段存在
    if (!signal.id) signal.id = R1Utils.generateId('signal');
    if (!signal.name) signal.name = '未命名信号';

    // 🚨 信号码绝对不能修改！如果没有信号码，这个信号就是无效的
    if (!signal.signalCode) {
      console.error('❌ 信号码缺失，无法标准化此信号:', signal);
      return null; // 返回null表示无法标准化
    }

    if (!signal.protocol) signal.protocol = 'NEC';
    if (!signal.type) signal.type = 'other';
    if (!signal.description) signal.description = '';

    // 确保布尔字段
    if (typeof signal.isLearned === 'string') {
      signal.isLearned = signal.isLearned === 'true' || signal.isLearned === '1';
    } else if (typeof signal.isLearned !== 'boolean') {
      signal.isLearned = true;
    }

    // 确保数字字段
    if (typeof signal.sentCount === 'string') {
      const parsed = parseInt(signal.sentCount);
      signal.sentCount = isNaN(parsed) ? 0 : parsed;
    } else if (typeof signal.sentCount !== 'number') {
      signal.sentCount = 0;
    }

    // 确保时间字段
    if (signal.lastSent && typeof signal.lastSent === 'string') {
      const parsed = parseInt(signal.lastSent);
      signal.lastSent = isNaN(parsed) ? null : parsed;
    }

    if (signal.created && typeof signal.created === 'string') {
      const parsed = parseInt(signal.created);
      signal.created = isNaN(parsed) ? Date.now() : parsed;
    } else if (!signal.created) {
      signal.created = Date.now();
    }

    return signal;
  }

  /**
   * 保存信号到本地存储 - 标准格式
   */
  saveSignalsToStorage() {
    console.warn('⚠️ 本地信号存储已移除，所有信号数据应保存到后端API');
    // 不再保存业务数据到localStorage
  }

  /**
   * 智能转换任意格式的信号数据到标准格式
   * @param {Object} rawData - 任意格式的原始数据
   * @returns {Object} - 转换后的数据对象
   */
  smartConvertSignalData(rawData) {
    const converted = {};

    // 深度提取嵌套数据
    const flatData = this.flattenNestedData(rawData);

    // 智能识别名称字段 (支持中英文) - 增强版
    converted.name = this.extractField(flatData, [
      'name', 'signalName', 'title', 'label', 'deviceName', 'command', 'commandName',
      'device_name', '设备名称', '名称', 'device.name', 'button.name', 'function',
      '🏠_device_name', 'RemoteSignal.Name', 'device.brand', 'remoteControl.button.name',
      'n', 'device.manufacturer'
    ]) || '未命名信号';

    // 智能识别信号码字段 - 增强版
    converted.signalCode = this.extractField(flatData, [
      'signalCode', 'code', 'signal', 'irCode', 'hex', 'data', 'value', 'rawData',
      'ir_code', '信号码', 'IRCode', 'irData', 'irSignal.rawData', 'command',
      'title', 'label', 'commandName', '📡_signal_hex', 'button.irSignal.rawData',
      'RemoteSignal.IRCode.#text', 'IRCode.#text'
    ]) || '';

    // 处理数组格式的信号码
    if (Array.isArray(converted.signalCode)) {
      converted.signalCode = converted.signalCode.join('');
    }

    // 处理特殊格式的信号码 (如 "NEC:0x1234")
    if (typeof converted.signalCode === 'string' && converted.signalCode.includes(':')) {
      const parts = converted.signalCode.split(':');
      if (parts.length === 2) {
        converted.protocol = parts[0].trim();
        converted.signalCode = parts[1].trim();
      }
    }

    // 智能识别协议字段 - 增强版
    if (!converted.protocol) {
      converted.protocol = this.extractField(flatData, [
        'protocol', 'protocolType', 'encoding', 'format', 'protocolInfo',
        'protocol_type', '协议类型', '协议', 'type', 'irSignal.protocol',
        '🔧_protocol_name', 'button.irSignal.protocol', 'RemoteSignal.IRCode.@protocol',
        'IRCode.@protocol', 'signal.type'
      ]) || 'NEC';
    }

    // 智能识别频率字段
    converted.frequency = this.extractField(flatData, [
      'frequency', 'freq', 'carrier', 'carrierFreq', 'carrier_freq',
      '载波频率', '频率', 'Frequency'
    ]) || '38000';

    // 清理频率格式 (移除单位)
    if (typeof converted.frequency === 'string') {
      converted.frequency = converted.frequency.replace(/[^\d]/g, '') || '38000';
    }

    // 智能识别设备类型字段 - 增强版
    converted.type = this.extractField(flatData, [
      'type', 'deviceType', 'category', 'device', 'brand', 'deviceCategory',
      'device_category', '设备类别', '类型', 'device.type', 'DeviceType',
      '🏷️_device_tag', 'remoteControl.device.type', 'RemoteSignal.DeviceType'
    ]) || 'other';

    // 标准化设备类型
    converted.type = this.normalizeDeviceType(converted.type);

    // 智能识别描述字段
    converted.description = this.extractField(flatData, [
      'description', 'desc', 'note', 'comment', 'info', '备注说明', '说明',
      'description_text', 'notes', 'metadata.description'
    ]) || '';

    // 智能识别学习状态字段
    const learnedValue = this.extractField(flatData, [
      'isLearned', 'learned', 'captured', 'isRecorded', 'is_learned_signal',
      'is_learned', '是否学习', 'IsLearned', 'usage.learned'
    ]);
    converted.isLearned = this.parseBoolean(learnedValue, false);

    // 智能识别统计字段
    const countValue = this.extractField(flatData, [
      'sentCount', 'count', 'used', 'times', 'usage', 'timesUsed', 'pressCount',
      'send_count', '使用次数', 'SendCount', 'usage.pressCount'
    ]);
    converted.sentCount = parseInt(countValue) || 0;

    // 智能识别时间字段
    const lastSentValue = this.extractField(flatData, [
      'lastSent', 'lastUsed', 'lastTime', 'lastPressed', 'last_send_time',
      'last_used', '最后使用', 'LastSent', 'usage.lastPressed'
    ]);
    converted.lastSent = this.parseTimestamp(lastSentValue);

    const createdValue = this.extractField(flatData, [
      'created', 'timestamp', 'time', 'date', 'create_time', 'createTime'
    ]);
    converted.created = this.parseTimestamp(createdValue) || Date.now();

    return converted;
  }

  /**
   * 扁平化嵌套数据
   */
  flattenNestedData(obj, prefix = '') {
    const flattened = {};

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const value = obj[key];
        const newKey = prefix ? `${prefix}.${key}` : key;

        if (value && typeof value === 'object' && !Array.isArray(value)) {
          // 递归处理嵌套对象
          Object.assign(flattened, this.flattenNestedData(value, newKey));
        } else {
          // 直接赋值
          flattened[newKey] = value;
          // 同时保留原始键名
          flattened[key] = value;
        }
      }
    }

    return flattened;
  }

  /**
   * 提取字段值
   */
  extractField(data, fieldNames) {
    for (const fieldName of fieldNames) {
      if (data[fieldName] !== undefined && data[fieldName] !== null && data[fieldName] !== '') {
        return data[fieldName];
      }
    }
    return null;
  }

  /**
   * 标准化设备类型 - 增强版
   */
  normalizeDeviceType(type) {
    if (!type) return 'other';

    const typeStr = type.toString().toLowerCase();

    // 电视相关
    if (typeStr.includes('tv') || typeStr.includes('电视') || typeStr.includes('television') ||
        typeStr.includes('机顶盒') || typeStr.includes('media')) {
      return 'tv';
    }

    // 空调相关
    if (typeStr.includes('ac') || typeStr.includes('空调') || typeStr.includes('airconditioner') ||
        typeStr.includes('air') || typeStr.includes('制冷') || typeStr.includes('制热')) {
      return 'ac';
    }

    // 灯光相关
    if (typeStr.includes('light') || typeStr.includes('灯') || typeStr.includes('lamp') ||
        typeStr.includes('lighting') || typeStr.includes('开关')) {
      return 'light';
    }

    // 风扇相关
    if (typeStr.includes('fan') || typeStr.includes('风扇') || typeStr.includes('风机') ||
        typeStr.includes('ceiling_fan')) {
      return 'fan';
    }

    // 音响相关
    if (typeStr.includes('audio') || typeStr.includes('音响') || typeStr.includes('speaker') ||
        typeStr.includes('sound')) {
      return 'audio';
    }

    // 投影仪相关
    if (typeStr.includes('projector') || typeStr.includes('投影') || typeStr.includes('projection')) {
      return 'projector';
    }

    // 媒体播放器
    if (typeStr.includes('media_player') || typeStr.includes('player') || typeStr.includes('盒子')) {
      return 'media';
    }

    return 'other';
  }

  /**
   * 解析布尔值
   */
  parseBoolean(value, defaultValue = false) {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
      const str = value.toLowerCase();
      return str === 'true' || str === '1' || str === 'yes' || str === 'on';
    }
    if (typeof value === 'number') {
      return value !== 0;
    }
    return defaultValue;
  }

  /**
   * 解析时间戳
   */
  parseTimestamp(value) {
    if (!value) return null;

    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseInt(value);
      if (!isNaN(parsed)) return parsed;

      const date = new Date(value);
      if (!isNaN(date.getTime())) return date.getTime();
    }

    return null;
  }

  /**
   * 创建标准格式的新信号
   * @param {Object} signalData - 信号数据
   * @returns {Object} - 标准格式信号
   */
  createStandardSignal(signalData) {
    // 🚨 信号码检查 - 如果没有信号码，返回null表示无法创建
    if (!signalData.signalCode) {
      console.error('❌ 无法创建信号：缺少信号码', signalData);
      return null;
    }

    return {
      id: signalData.id || R1Utils.generateId('signal'),
      name: signalData.name || '未命名信号',
      signalCode: signalData.signalCode, // 🚨 绝对不修改信号码
      protocol: signalData.protocol || 'NEC',
      frequency: signalData.frequency || '38000',
      type: signalData.type || 'other',
      description: signalData.description || this.getTypeName(signalData.type || 'other'),
      isLearned: signalData.isLearned !== undefined ? signalData.isLearned : true,
      sentCount: signalData.sentCount || 0,
      lastSent: signalData.lastSent || null,
      created: signalData.created || Date.now()
    };
  }

  /**
   * 从ESP32加载信号
   */
  async loadSignalsFromESP32() {
    try {
      const response = await this.requestESP32('/api/signals');
      if (response.success && response.data) {
        response.data.forEach(signal => {
          this.signals.set(signal.id, signal);
        });
        console.log(`✅ SignalManager: 从ESP32加载了 ${response.data.length} 个信号`);
      }
    } catch (error) {
      console.warn('SignalManager: ESP32连接失败，使用本地数据');
    }
  }

  /**
   * 更新统计数据
   */
  updateStats() {
    this.stats.totalSignals = this.signals.size;

    // 更新UI中的统计显示
    const totalEl = $('#totalSignalsCount');
    if (totalEl) {
      totalEl.textContent = this.stats.totalSignals;
    }
  }

  /**
   * 渲染信号列表/网格 - 符合架构标准
   */
  renderSignals() {
    const gridContainer = $('#signalsGrid');
    const listContainer = $('#signalsList');

    if (!gridContainer || !listContainer) {
      console.error('SignalManager: 找不到信号容器元素');
      return;
    }

    // 性能优化：检查是否需要重新渲染
    const filteredSignals = this.getFilteredSignals();
    const currentSignalsHash = this.getSignalsHash(filteredSignals);

    console.log(`🔍 渲染信号 - 总数: ${this.signals.size}, 过滤后: ${filteredSignals.length}`);
    console.log(`🔍 搜索关键词: "${this.searchKeyword}", 类型过滤: "${this.filterType}", 排序: "${this.sortBy}"`);

    // 详细显示过滤过程
    if (this.signals.size !== filteredSignals.length) {
      console.log(`🔍 过滤详情:`);
      console.log(`  - 原始信号: ${this.signals.size} 个`);

      let afterSearch = Array.from(this.signals.values());
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        afterSearch = afterSearch.filter(signal =>
          signal.name.toLowerCase().includes(keyword) ||
          signal.description?.toLowerCase().includes(keyword)
        );
        console.log(`  - 搜索过滤后: ${afterSearch.length} 个 (关键词: "${this.searchKeyword}")`);
      }

      if (this.filterType) {
        afterSearch = afterSearch.filter(signal => signal.type === this.filterType);
        console.log(`  - 类型过滤后: ${afterSearch.length} 个 (类型: "${this.filterType}")`);
      }

      console.log(`  - 最终显示: ${filteredSignals.length} 个`);
    }

    if (this._lastSignalsHash === currentSignalsHash &&
        this._lastView === this.currentView &&
        this._lastMultiSelectMode === this.isMultiSelectMode) {
      console.log('🔍 使用缓存，跳过渲染');
      return;
    }

    // 使用DocumentFragment减少重排重绘
    if (this.currentView === 'grid') {
      gridContainer.style.display = 'grid';
      listContainer.style.display = 'none';
      this.renderSignalGridOptimized(gridContainer, filteredSignals);
    } else {
      gridContainer.style.display = 'none';
      listContainer.style.display = 'block';
      this.renderSignalListOptimized(listContainer, filteredSignals);
    }

    // 缓存渲染状态
    this._lastSignalsHash = currentSignalsHash;
    this._lastView = this.currentView;
    this._lastMultiSelectMode = this.isMultiSelectMode;
  }

  /**
   * 获取过滤后的信号
   */
  getFilteredSignals() {
    let signals = Array.from(this.signals.values());

    // 搜索过滤
    if (this.searchKeyword) {
      const keyword = this.searchKeyword.toLowerCase();
      signals = signals.filter(signal =>
        signal.name.toLowerCase().includes(keyword) ||
        signal.description?.toLowerCase().includes(keyword)
      );
    }

    // 类型过滤
    if (this.filterType) {
      signals = signals.filter(signal => signal.type === this.filterType);
    }

    // 排序
    signals.sort((a, b) => {
      switch (this.sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'created':
          return b.created - a.created;
        case 'used':
          return b.sentCount - a.sentCount;
        default:
          return 0;
      }
    });

    return signals;
  }

  /**
   * 计算信号数据哈希 - 用于检测变化
   */
  getSignalsHash(signals) {
    const hashData = signals.map(s => `${s.id}-${s.name}-${s.sentCount}-${this.selectedSignals.has(s.id)}`).join('|');
    return hashData.length + '-' + (hashData.charCodeAt(0) || 0);
  }

  /**
   * 渲染信号网格 - 性能优化版本
   */
  renderSignalGridOptimized(container, signals) {
    if (signals.length === 0) {
      container.innerHTML = '<div class="no-signals">暂无信号数据</div>';
      return;
    }

    console.log(`🔍 开始渲染 ${signals.length} 个信号到网格`);

    // 🔧 使用DocumentFragment减少DOM操作
    const fragment = document.createDocumentFragment();
    const tempDiv = R1Utils.dom.create('div');

    // 批量生成HTML - 增加错误处理
    try {
      const htmlParts = [];
      signals.forEach((signal, index) => {
        try {
          const html = this.createSignalCardHTML(signal);
          htmlParts.push(html);
          console.log(`✅ 信号 ${index + 1}: ${signal.name} HTML生成成功`);
        } catch (error) {
          console.error(`❌ 信号 ${index + 1}: ${signal.name} HTML生成失败:`, error, signal);
        }
      });

      const html = htmlParts.join('');
      tempDiv.innerHTML = html;

      console.log(`📊 HTML生成完成，共 ${htmlParts.length} 个信号，DOM子元素: ${tempDiv.children.length}`);

      // 移动所有子元素到fragment
      while (tempDiv.firstChild) {
        fragment.appendChild(tempDiv.firstChild);
      }

      // 一次性替换内容
      container.innerHTML = '';
      container.appendChild(fragment);

      console.log(`✅ 网格渲染完成，容器子元素数量: ${container.children.length}`);

    } catch (error) {
      console.error('❌ 网格渲染过程出错:', error);
      container.innerHTML = '<div class="error">渲染出错，请刷新页面</div>';
    }
  }

  /**
   * 渲染信号列表 - 性能优化版本
   */
  renderSignalListOptimized(container, signals) {
    if (signals.length === 0) {
      container.innerHTML = '<div class="no-signals">暂无信号数据</div>';
      return;
    }

    // 🔧 使用DocumentFragment减少DOM操作
    const fragment = document.createDocumentFragment();
    const tempDiv = document.createElement('div');

    // 批量生成HTML
    const html = signals.map(signal => this.createSignalListItemHTML(signal)).join('');
    tempDiv.innerHTML = html;

    // 移动所有子元素到fragment
    while (tempDiv.firstChild) {
      fragment.appendChild(tempDiv.firstChild);
    }

    // 一次性替换内容
    container.innerHTML = '';
    container.appendChild(fragment);
  }



  /**
   * 创建信号卡片HTML - 优化版，简洁清晰
   */
  createSignalCardHTML(signal) {
    try {
      // 数据安全检查
      if (!signal || !signal.id) {
        console.error('❌ 信号数据无效:', signal);
        return '<div class="signal-card error">信号数据错误</div>';
      }

      // 由于所有数据都已统一为标准格式，直接使用即可
      const isSelected = this.selectedSignals.has(signal.id);
      const typeIcon = this.getTypeIcon(signal.type);

      // 优化时间显示 - 添加年份，更清晰
      const createdText = signal.created ?
        R1Utils.formatTime(signal.created, 'YYYY-MM-DD HH:mm') : '未知时间';

      // 安全的字符串处理
      const safeName = (signal.name || '未命名').replace(/"/g, '&quot;');
      const safeSignalCode = (signal.signalCode || 'N/A').replace(/"/g, '&quot;');
      const safeProtocol = (signal.protocol || 'N/A').replace(/"/g, '&quot;');
      const safeId = (signal.id || '').replace(/"/g, '&quot;');

      return `
        <div class="signal-card ${isSelected ? 'selected' : ''} ${this.isMultiSelectMode ? 'multiselect-mode' : ''}"
             data-signal-id="${safeId}"
             ${this.isMultiSelectMode ? 'data-action="toggle-selection"' : ''}>
          ${this.isMultiSelectMode ? `
            <div class="signal-checkbox">
              <input type="checkbox" ${isSelected ? 'checked' : ''}
                     data-action="toggle-selection-checkbox" data-signal-id="${safeId}">
            </div>
          ` : ''}
          <div class="signal-header">
            <div class="signal-icon">${typeIcon}</div>
            <div class="signal-name">${safeName}</div>
            ${signal.isLearned ? '<div class="signal-badge learned">学习</div>' : '<div class="signal-badge manual">手动</div>'}
          </div>
          <div class="signal-code-info">
            <div class="signal-code">${safeSignalCode}</div>
            <div class="signal-protocol">${safeProtocol}</div>
          </div>
          <div class="signal-info">
            <div class="signal-type">${this.getTypeName(signal.type)}</div>
            <div class="signal-created">${createdText}</div>
          </div>
          <div class="signal-actions">
            <button class="btn small primary" data-action="send-signal" data-signal-id="${safeId}">发射</button>
            <button class="btn small secondary" data-action="edit-signal" data-signal-id="${safeId}">编辑</button>
            <button class="btn small secondary" data-action="show-details" data-signal-id="${safeId}">详情</button>
            <button class="btn small danger" data-action="delete-signal" data-signal-id="${safeId}">删除</button>
          </div>
        </div>
      `;
    } catch (error) {
      console.error('❌ 创建信号卡片HTML失败:', error, signal);
      return '<div class="signal-card error">HTML生成错误</div>';
    }
  }

  /**
   * 创建信号列表项HTML - 优化版，简洁清晰
   */
  createSignalListItemHTML(signal) {
    const isSelected = this.selectedSignals.has(signal.id);
    const typeIcon = this.getTypeIcon(signal.type);

    // 优化时间显示 - 添加年份，更清晰
    const createdText = signal.created ?
      R1Utils.formatTime(signal.created, 'YYYY-MM-DD HH:mm') : '未知时间';

    return `
      <div class="signal-list-item ${isSelected ? 'selected' : ''} ${this.isMultiSelectMode ? 'multiselect-mode' : ''}"
           data-signal-id="${signal.id}"
           ${this.isMultiSelectMode ? 'data-action="toggle-selection"' : ''}>
        ${this.isMultiSelectMode ? `
          <div class="signal-checkbox">
            <input type="checkbox" ${isSelected ? 'checked' : ''}
                   data-action="toggle-selection-checkbox" data-signal-id="${signal.id}">
          </div>
        ` : ''}
        <div class="signal-icon">${typeIcon}</div>
        <div class="signal-name">${signal.name}</div>
        <div class="signal-code">${signal.signalCode || 'N/A'}</div>
        <div class="signal-protocol">${signal.protocol || 'N/A'}</div>
        <div class="signal-badge ${signal.isLearned ? 'learned' : 'manual'}">${signal.isLearned ? '学习' : '手动'}</div>
        <div class="signal-created">${createdText}</div>
        <div class="signal-actions">
          <button class="btn small primary" data-action="send-signal" data-signal-id="${signal.id}">发射</button>
          <button class="btn small secondary" data-action="edit-signal" data-signal-id="${signal.id}">编辑</button>
          <button class="btn small secondary" data-action="show-details" data-signal-id="${signal.id}">详情</button>
          <button class="btn small danger" data-action="delete-signal" data-signal-id="${signal.id}">删除</button>
        </div>
      </div>
    `;
  }

  /**
   * 获取类型图标
   */
  getTypeIcon(type) {
    const icons = {
      tv: '📺',
      ac: '❄️',
      light: '💡',
      fan: '🌀',
      other: '📱'
    };
    return icons[type] || icons.other;
  }

  /**
   * 获取类型名称
   */
  getTypeName(type) {
    const names = {
      tv: '电视',
      ac: '空调',
      light: '灯光',
      fan: '风扇',
      other: '其他'
    };
    return names[type] || names.other;
  }

  /**
   * 切换视图模式 - 性能优化版本
   */
  toggleView() {
    const oldView = this.currentView;
    this.currentView = this.currentView === 'grid' ? 'list' : 'grid';

    console.log(`🔧 SignalManager: 视图切换 ${oldView} -> ${this.currentView}`);

    // 🔧 强制重新渲染（因为视图变化）
    this._lastView = null;
    this.renderSignals();
    this.updateViewToggleButton();
  }

  /**
   * 更新视图切换按钮
   */
  updateViewToggleButton() {
    const btn = $('#signalViewToggle');
    if (btn) {
      const icon = btn.querySelector('.btn-icon');
      const text = btn.querySelector('.btn-text');

      if (this.currentView === 'grid') {
        if (icon) icon.textContent = '📋';
        if (text) text.textContent = '列表视图';
      } else {
        if (icon) icon.textContent = '⊞';
        if (text) text.textContent = '网格视图';
      }
    }
  }

  /**
   * 切换多选模式 - 性能优化版本
   */
  toggleMultiSelectMode() {
    const oldMode = this.isMultiSelectMode;
    this.isMultiSelectMode = !this.isMultiSelectMode;

    console.log(`🔧 SignalManager: 多选模式切换 ${oldMode} -> ${this.isMultiSelectMode}`);

    // 清除选择
    this.selectedSignals.clear();

    // 🔧 强制重新渲染（因为多选模式变化）
    this._lastMultiSelectMode = null;
    this.renderSignals();
    this.updateMultiSelectButton();
    this.updateBatchOperationsVisibility();
  }

  /**
   * 更新多选按钮
   */
  updateMultiSelectButton() {
    const btn = $('#multiSelectToggleBtn');
    if (btn) {
      const text = btn.querySelector('.btn-text');
      if (text) {
        text.textContent = this.isMultiSelectMode ? '退出多选' : '多选模式';
      }

      if (this.isMultiSelectMode) {
        btn.classList.add('active');
      } else {
        btn.classList.remove('active');
      }
    }
  }

  /**
   * 更新批量操作区域可见性
   */
  updateBatchOperationsVisibility() {
    const batchOps = $('#batchOperations');
    if (batchOps) {
      batchOps.style.display = this.isMultiSelectMode ? 'flex' : 'none';
    }
  }

  /**
   * 切换搜索区域
   */
  toggleSearchArea() {
    const searchArea = $('#searchFilterArea');
    if (searchArea) {
      const isVisible = searchArea.style.display !== 'none';
      searchArea.style.display = isVisible ? 'none' : 'block';
    }
  }

  /**
   * 清除所有过滤条件
   */
  clearAllFilters() {
    // 清除搜索关键词
    this.searchKeyword = '';
    const searchInput = $('#signalSearchInput');
    if (searchInput) {
      searchInput.value = '';
    }

    // 清除类型过滤
    this.filterType = '';
    const typeFilter = $('#signalTypeFilter');
    if (typeFilter) {
      typeFilter.value = '';
    }

    // 重置排序
    this.sortBy = 'name';
    const sortSelect = $('#signalSortBy');
    if (sortSelect) {
      sortSelect.value = 'name';
    }


  }



  /**
   * 全选信号
   */
  selectAllSignals() {
    const filteredSignals = this.getFilteredSignals();
    filteredSignals.forEach(signal => {
      this.selectedSignals.add(signal.id);
    });
    this.renderSignals();
    this.updateSelectedCount();
  }

  /**
   * 全不选信号
   */
  selectNoneSignals() {
    this.selectedSignals.clear();
    this.renderSignals();
    this.updateSelectedCount();
  }

  /**
   * 更新选中数量显示
   */
  updateSelectedCount() {
    const countEl = $('#selectedCount');
    if (countEl) {
      countEl.textContent = this.selectedSignals.size;
    }
  }

  /**
   * 切换信号选择状态
   */
  toggleSignalSelection(signalId) {
    if (this.selectedSignals.has(signalId)) {
      this.selectedSignals.delete(signalId);
    } else {
      this.selectedSignals.add(signalId);
    }
    this.renderSignals();
    this.updateSelectedCount();
  }

  /**
   * 处理复选框切换
   */
  handleCheckboxToggle(signalId, event) {
    const checkbox = event.target.closest('input[type="checkbox"]') || event.target;
    if (checkbox.checked) {
      this.selectedSignals.add(signalId);
    } else {
      this.selectedSignals.delete(signalId);
    }
    this.updateSelectedCount();
  }

  /**
   * 显示学习对话框
   */
  showLearnDialog() {
    const modalContent = `
      <div class="modal-header">
        <h3>学习新信号</h3>
      </div>
      <div class="modal-body">
        <form id="learnSignalForm">
          <div class="form-group">
            <label for="signalName">信号名称</label>
            <input type="text" id="signalName" class="form-input" placeholder="请输入信号名称" required>
          </div>
          <div class="form-group">
            <label for="signalType">信号类型</label>
            <select id="signalType" class="form-input">
              <option value="tv">电视</option>
              <option value="ac">空调</option>
              <option value="fan">风扇</option>
              <option value="light">灯光</option>
              <option value="other">其他</option>
            </select>
          </div>
          <div class="form-group">
            <label for="signalDescription">描述 (可选)</label>
            <textarea id="signalDescription" class="form-input" placeholder="信号描述" rows="3"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">取消</button>
          <button type="button" class="btn primary" data-action="submit-learn-form">开始学习</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);

    // 直接聚焦到名称输入框，移除不必要的定时器
    const nameInput = $('#signalName');
    if (nameInput) nameInput.focus();
  }

  /**
   * 发射信号 - 符合事件驱动架构，只负责管理不执行发射
   */
  async sendSignal(signalId) {
    const signal = this.signals.get(signalId);
    if (!signal) {
      this.handleError(new Error('信号不存在'), '信号发射');
      return false;
    }

    try {
      // 通过控制模块发射单个信号
      // 信号管理模块只负责管理，不执行发射逻辑
      this.eventBus.emitSync('control.request.send-signal', {
        signalId: signal.id,
        source: 'SignalManager'
      });

      // 信号管理模块只负责发送请求，不执行发射
      // 发射逻辑、统计更新、UI更新都由控制模块的统一系统处理
      return true;

    } catch (error) {
      console.error(`❌ [DEBUG] SignalManager.sendSignal() 失败:`, error);
      this.handleError(error, '信号发射请求');
      return false;
    }
  }

  /**
   * 处理信号发射成功事件 - 由控制模块通知
   */
  handleSignalEmitSuccess(data) {
    try {
      const { signalId, signalName, timestamp } = data;
      const signal = this.signals.get(signalId);

      if (signal) {
        // 更新信号统计
        signal.lastSent = timestamp;
        signal.sentCount = (signal.sentCount || 0) + 1;
        this.stats.totalSent++;

        // 本地存储已移除 - 数据应保存到后端

        // 更新UI
        this.renderSignals();
        this.updateStats();

        console.log(`✅ SignalManager: 信号发射成功统计已更新: ${signalName}`);
      }
    } catch (error) {
      console.error('SignalManager: 处理信号发射成功事件失败:', error);
    }
  }

  /**
   * 处理信号发射失败事件 - 由控制模块通知
   */
  handleSignalEmitFailed(data) {
    try {
      const { signalId, signalName, error } = data;
      console.warn(`⚠️ SignalManager: 信号发射失败: ${signalName}, 错误: ${error}`);

      // 可以在这里添加失败统计或其他处理逻辑
      // 目前只记录日志
    } catch (err) {
      console.error('SignalManager: 处理信号发射失败事件失败:', err);
    }
  }

  /**
   * 处理信号追加事件 - 同步选中状态
   */
  handleSignalsAppended(data) {
    try {


      // 将追加的信号添加到选中状态，保持UI与当前任务队列同步
      if (data.appendedSignals && Array.isArray(data.appendedSignals)) {
        data.appendedSignals.forEach(signal => {
          this.selectedSignals.add(signal.id);
        });

        // 更新UI显示 - 重新渲染信号列表以显示新的选中状态
        this.renderSignals();
        this.updateSelectedCount();


      }

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '处理信号追加事件';
    } catch (error) {
      console.error('SignalManager: 处理信号追加事件失败:', error);
    }
  }

  /**
   * 删除信号
   */
  async deleteSignal(signalId) {
    const signal = this.signals.get(signalId);
    if (!signal) {
      this.handleError(new Error('信号不存在'), '信号删除');
      return false;
    }

    if (!confirm(`确定要删除信号 "${signal.name}" 吗？此操作不可恢复！`)) {
      return false;
    }

    try {
      // 从本地删除
      this.signals.delete(signalId);
      this.selectedSignals.delete(signalId);

      // 本地存储已移除 - 数据应保存到后端

      // 更新UI
      this.renderSignals();
      this.updateStats();
      this.updateSelectedCount();

      this.handleSuccess(`信号 "${signal.name}" 删除成功`, '信号删除');
      return true;

    } catch (error) {
      this.handleError(error, '信号删除');
      return false;
    }
  }

  /**
   * 显示信号详情 - 支持新的信号格式
   */
  showSignalDetails(signalId) {
    const signal = this.signals.get(signalId);
    if (!signal) {
      this.handleError(new Error('信号不存在'), '信号详情');
      return;
    }

    const modalContent = `
      <div class="modal-header">
        <h3>信号详情</h3>
      </div>
      <div class="modal-body">
        <div class="detail-grid">
          <div class="detail-item">
            <label>名称</label>
            <span>${signal.name}</span>
          </div>
          <div class="detail-item">
            <label>信号码</label>
            <span class="signal-code-display">${signal.signalCode || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <label>协议类型</label>
            <span class="signal-protocol-display">${signal.protocol || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <label>信号来源</label>
            <span class="signal-source">${signal.isLearned ? '学习获得' : '手动添加'}</span>
          </div>
          <div class="detail-item">
            <label>设备类型</label>
            <span>${this.getTypeName(signal.type)}</span>
          </div>
          <div class="detail-item">
            <label>描述</label>
            <span>${signal.description || '无'}</span>
          </div>
          <div class="detail-item">
            <label>载波频率</label>
            <span>${signal.frequency} Hz</span>
          </div>
          <div class="detail-item">
            <label>创建时间</label>
            <span>${R1Utils.formatTime(signal.created, 'YYYY-MM-DD HH:mm:ss')}</span>
          </div>
          <div class="detail-item">
            <label>发射次数</label>
            <span>${signal.sentCount}</span>
          </div>
          <div class="detail-item">
            <label>最后发射</label>
            <span>${signal.lastSent ? R1Utils.formatTime(signal.lastSent, 'YYYY-MM-DD HH:mm:ss') : '未发射'}</span>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">关闭</button>
          <button type="button" class="btn primary" data-action="send-signal" data-signal-id="${signal.id}">发射信号</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);
  }

  /**
   * 批量发射信号 - 符合事件驱动架构，只负责管理不执行发射
   */
  async batchSendSignals() {
    if (this.selectedSignals.size === 0) {
      this.handleError(new Error('请先选择要发射的信号'), '批量发射');
      return;
    }

    const signalIds = Array.from(this.selectedSignals);
    console.log(`🚀 [DEBUG] SignalManager.batchSendSignals() 开始 - 信号数量: ${signalIds.length}, IDs: ${signalIds}`);

    try {
      // 🔥 统一事件：通过控制模块发射选中信号
      // 信号管理模块只负责管理，不执行发射逻辑
      console.log(`📤 [DEBUG] SignalManager: 即将发送 control.request.selected-signals 事件`);
      this.eventBus.emitSync('control.request.selected-signals', {
        signalIds: signalIds,
        source: 'SignalManager'
      });

      // ✅ 信号管理模块只负责发送请求，不执行发射
      // 发射逻辑、进度更新、统计更新都由控制模块的统一系统处理
      console.log(`✅ [DEBUG] SignalManager: 已向控制模块发送批量信号发射请求，信号数量: ${signalIds.length}`);

      this.handleSuccess(`已提交批量发射请求 (${signalIds.length} 个信号)`, '批量发射');

    } catch (error) {
      console.error(`❌ [DEBUG] SignalManager.batchSendSignals() 失败:`, error);
      this.handleError(error, '批量发射请求');
    }
  }

  /**
   * 批量删除信号
   */
  async batchDeleteSignals() {
    if (this.selectedSignals.size === 0) {
      this.handleError(new Error('请先选择要删除的信号'), '批量删除');
      return;
    }

    const signalIds = Array.from(this.selectedSignals);
    const signalNames = signalIds.map(id => this.signals.get(id)?.name).filter(Boolean);

    if (!confirm(`确定要删除这 ${signalIds.length} 个信号吗？\n\n${signalNames.join('\n')}\n\n此操作不可恢复！`)) {
      return;
    }

    let successCount = 0;
    let errorCount = 0;

    for (const signalId of signalIds) {
      try {
        const success = await this.deleteSignal(signalId);
        if (success) {
          successCount++;
        } else {
          errorCount++;
        }
      } catch (error) {
        errorCount++;
      }
    }

    const message = `批量删除完成: 成功 ${successCount} 个，失败 ${errorCount} 个`;
    if (errorCount === 0) {
      this.handleSuccess(message, '批量删除');
    } else {
      this.handleError(new Error(message), '批量删除');
    }

    // 清除选择
    this.selectedSignals.clear();
    this.updateSelectedCount();
  }

  /**
   * 导出所有信号
   */
  exportAllSignals() {
    if (this.signals.size === 0) {
      this.handleError(new Error('没有信号可以导出'), '信号导出');
      return;
    }

    const allSignals = Array.from(this.signals.values());
    const exportData = {
      version: '3.0.0',
      exported: Date.now(),
      exportType: 'all',
      totalSignals: allSignals.length,
      signals: allSignals
    };

    this.downloadJSON(exportData, `r1-all-signals-${R1Utils.formatTime(Date.now(), 'YYYY-MM-DD-HH-mm-ss')}.json`);
    this.handleSuccess(`已导出全部 ${allSignals.length} 个信号`, '信号导出');
  }

  /**
   * 导出选中信号
   */
  exportSelectedSignals() {
    if (this.selectedSignals.size === 0) {
      this.handleError(new Error('请先选择要导出的信号'), '信号导出');
      return;
    }

    const selectedSignals = Array.from(this.selectedSignals)
      .map(id => this.signals.get(id))
      .filter(Boolean);

    const exportData = {
      version: '3.0.0',
      exported: Date.now(),
      exportType: 'selected',
      totalSignals: selectedSignals.length,
      signals: selectedSignals
    };

    this.downloadJSON(exportData, `r1-selected-signals-${R1Utils.formatTime(Date.now(), 'YYYY-MM-DD-HH-mm-ss')}.json`);
    this.handleSuccess(`已导出选中的 ${selectedSignals.length} 个信号`, '信号导出');
  }

  /**
   * 下载JSON文件
   */
  downloadJSON(data, filename) {
    const dataStr = JSON.stringify(data, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = R1Utils.dom.create('a');
    a.href = url;
    a.download = filename;
    a.click();

    URL.revokeObjectURL(url);
  }

  /**
   * 显示导入对话框
   */
  showImportDialog() {
    const modalContent = `
      <div class="modal-header">
        <h3>导入信号</h3>
      </div>
      <div class="modal-body">
        <div class="import-options">
          <button type="button" class="btn primary" data-action="import-from-file">从文件导入</button>
          <button type="button" class="btn secondary" data-action="import-from-text">从文本导入</button>
        </div>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">取消</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);
  }

  /**
   * 从文件导入信号
   */
  importFromFile() {
    const modalContent = `
      <div class="modal-header">
        <h3>从文件导入信号</h3>
      </div>
      <div class="modal-body">
        <div class="import-file-area">
          <div class="form-group">
            <label for="importFileInput">选择信号文件</label>
            <input type="file" id="importFileInput" class="form-input" accept=".json,.txt,.xml,.md" required>
            <div class="form-help">
              支持格式：JSON、TXT、XML、MD
            </div>
          </div>
          <div class="import-preview" id="importPreview" style="display: none;">
            <h4>预览导入数据</h4>
            <div class="preview-content" id="previewContent"></div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">取消</button>
          <button type="button" class="btn primary" id="confirmFileImport" disabled>确认导入</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);
    this.initFileImportHandlers();
  }

  /**
   * 从文本导入信号
   */
  importFromText() {
    const modalContent = `
      <div class="modal-header">
        <h3>从文本导入信号</h3>
      </div>
      <div class="modal-body">
        <div class="import-text-area">
          <div class="form-group">
            <label for="importTextInput">信号数据</label>
            <textarea id="importTextInput" class="form-input" rows="8" placeholder="请输入信号数据，格式：&#10;信号码,协议类型,信号名称,描述&#10;&#10;示例：&#10;0x20DF10EF,NEC,客厅电视开关,客厅电视的开关信号&#10;0x30EF20DF,NEC,空调制冷,空调制冷模式&#10;0x1234,RC5,卧室灯开关,卧室主灯开关"></textarea>
            <div class="form-help">
              格式：信号码,协议类型,信号名称,描述（每行一个信号）
            </div>
          </div>
          <div class="form-group">
            <label for="defaultSignalType">默认信号类型</label>
            <select id="defaultSignalType" class="form-input">
              <option value="tv">电视</option>
              <option value="ac">空调</option>
              <option value="fan">风扇</option>
              <option value="light">灯光</option>
              <option value="other">其他</option>
            </select>
          </div>
          <div class="import-preview" id="textImportPreview" style="display: none;">
            <h4>预览导入数据</h4>
            <div class="preview-content" id="textPreviewContent"></div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">取消</button>
          <button type="button" class="btn secondary" id="previewTextImport">预览</button>
          <button type="button" class="btn primary" id="confirmTextImport" disabled>确认导入</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);
    this.initTextImportHandlers();
  }

  /**
   * 显示编辑对话框 - 更新为新的信号格式
   */
  showEditDialog(signalId) {
    const signal = this.signals.get(signalId);
    if (!signal) {
      this.handleError(new Error('信号不存在'), '信号编辑');
      return;
    }

    const modalContent = `
      <div class="modal-header">
        <h3>编辑信号</h3>
      </div>
      <div class="modal-body">
        <form id="editSignalForm">
          <div class="form-group">
            <label for="editSignalCode">信号码</label>
            <input type="text" id="editSignalCode" class="form-input" value="${signal.signalCode || ''}" ${signal.isLearned ? 'readonly' : ''} placeholder="0x20DF10EF">
            ${signal.isLearned ? '<div class="form-help">学习获得的信号码不可修改</div>' : ''}
          </div>
          <div class="form-group">
            <label for="editSignalProtocol">协议类型</label>
            <select id="editSignalProtocol" class="form-input" ${signal.isLearned ? 'disabled' : ''}>
              <option value="NEC" ${signal.protocol === 'NEC' ? 'selected' : ''}>NEC</option>
              <option value="RC5" ${signal.protocol === 'RC5' ? 'selected' : ''}>RC5</option>
              <option value="SONY" ${signal.protocol === 'SONY' ? 'selected' : ''}>SONY</option>
              <option value="RAW" ${signal.protocol === 'RAW' ? 'selected' : ''}>RAW</option>
              <option value="OTHER" ${signal.protocol === 'OTHER' ? 'selected' : ''}>其他</option>
            </select>
            ${signal.isLearned ? '<div class="form-help">学习获得的协议类型不可修改</div>' : ''}
          </div>
          <div class="form-group">
            <label for="editSignalName">信号名称</label>
            <input type="text" id="editSignalName" class="form-input" value="${signal.name}" required>
          </div>
          <div class="form-group">
            <label for="editSignalType">信号类型</label>
            <select id="editSignalType" class="form-input">
              <option value="tv" ${signal.type === 'tv' ? 'selected' : ''}>电视</option>
              <option value="ac" ${signal.type === 'ac' ? 'selected' : ''}>空调</option>
              <option value="fan" ${signal.type === 'fan' ? 'selected' : ''}>风扇</option>
              <option value="light" ${signal.type === 'light' ? 'selected' : ''}>灯光</option>
              <option value="other" ${signal.type === 'other' ? 'selected' : ''}>其他</option>
            </select>
          </div>
          <div class="form-group">
            <label for="editSignalDescription">描述</label>
            <textarea id="editSignalDescription" class="form-input" rows="3">${signal.description || ''}</textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" data-action="close-modal">取消</button>
          <button type="button" class="btn primary" data-action="submit-edit-form" data-signal-id="${signal.id}">保存</button>
        </div>
      </div>
    `;

    window.R1System.showModal(modalContent);
  }
  /**
   * 处理编辑表单提交 - 支持新的信号格式
   */
  handleEditFormSubmit(signalId) {
    try {
      const signal = this.signals.get(signalId);
      if (!signal) {
        this.handleError(new Error('信号不存在'), '信号编辑');
        return;
      }

      // 获取表单数据
      const signalCode = $('#editSignalCode')?.value?.trim();
      const protocol = $('#editSignalProtocol')?.value;
      const name = $('#editSignalName')?.value?.trim();
      const type = $('#editSignalType')?.value;
      const description = $('#editSignalDescription')?.value?.trim();

      // 验证必填字段
      if (!name) {
        this.handleError(new Error('信号名称不能为空'), '信号编辑');
        return;
      }

      // 如果不是学习获得的信号，验证信号码和协议
      if (!signal.isLearned) {
        if (!signalCode || !this.validateSignalCode(signalCode)) {
          this.handleError(new Error('信号码格式不正确'), '信号编辑');
          return;
        }

        if (!protocol || !this.validateProtocol(protocol)) {
          this.handleError(new Error('协议类型不正确'), '信号编辑');
          return;
        }

        // 检查信号码是否与其他信号重复
        const existingSignal = Array.from(this.signals.values())
          .find(s => s.id !== signalId && s.signalCode === signalCode && s.protocol === protocol);

        if (existingSignal) {
          this.handleError(new Error('该信号码已存在'), '信号编辑');
          return;
        }
      }

      // 更新信号数据
      const updatedSignal = {
        ...signal,
        name: name,
        type: type,
        description: description
      };

      // 如果不是学习获得的信号，更新信号码和协议
      if (!signal.isLearned) {
        updatedSignal.signalCode = signalCode;
        updatedSignal.protocol = protocol;
      }

      // 保存更新 - 应通过后端API
      this.signals.set(signalId, updatedSignal);
      // 本地存储已移除 - 数据应保存到后端

      // 更新UI
      this.renderSignals();

      // 关闭模态框
      window.R1System.hideModal();

      this.handleSuccess(`信号 "${name}" 编辑成功`, '信号编辑');

    } catch (error) {
      this.handleError(error, '信号编辑');
    }
  }

  /**
   * 处理学习表单提交 - 直接开始学习
   */
  handleLearnFormSubmit() {
    try {
      // 关闭模态框
      window.R1System.hideModal();

      // 直接开始学习（新的学习流程不需要预设名称）
      this.startLearning();

    } catch (error) {
      this.handleError(error, '信号学习');
    }
  }

  // ==================== 增强版信号学习功能 ====================

  /**
   * 开始学习信号 - 统一入口
   */
  async startLearning() {
    try {
      // 检查是否已在学习状态
      if (this.learningState.isLearning) {
        this.handleError(new Error('已在学习模式中'), '开始学习');
        return;
      }

      console.log('SignalManager: 开始学习信号');

      // 暂停所有正在运行的任务
      await this.pauseAllTasks();

      // 设置学习状态
      this.learningState.isLearning = true;
      this.learningState.hasUnsavedSignal = false;
      this.learningState.currentSignalData = null;
      this.learningState.learningStartTime = Date.now();
      this.updateActivityTime();

      // 更新UI显示
      this.showLearningIndicator();
      this.updateLearningUI();

      // 发送学习开始指令到ESP32
      await this.sendLearningCommand('start');

      // 发布学习开始事件
      this.emitEvent('signal.learning.started', {
        timestamp: this.learningState.learningStartTime
      });

      this.handleSuccess('学习模式已启动，请对准设备按下遥控器', '开始学习');

      // 启动学习检测流程
      this.startLearningDetection();

    } catch (error) {
      console.error('SignalManager: 开始学习失败:', error);
      this.handleError(error, '开始学习');
      this.stopLearningAndCleanup();
    }
  }

  /**
   * 停止学习信号 - 手动触发
   */
  async stopLearning() {
    try {
      console.log('SignalManager: 用户主动停止学习信号');

      // 立即清理所有定时器，防止竞态条件
      this.clearLearningTimers();

      // 检查是否有未保存的信号 - 手动停止特有逻辑
      if (this.learningState.hasUnsavedSignal) {
        const confirmed = confirm('有未保存的信号，确定要停止学习吗？未保存的信号将丢失。');
        if (!confirmed) {
          console.log('SignalManager: 用户取消停止学习');
          // 如果用户取消，需要重新启动学习检测
          if (this.learningState.isLearning) {
            this.startLearningDetection();
          }
          return;
        }
      }

      // 调用共用的清理方法
      await this.stopLearningAndCleanup();

      // 手动停止的成功提示
      this.handleSuccess('学习模式已停止', '停止学习');

    } catch (error) {
      console.error('SignalManager: 停止学习失败:', error);
      this.handleError(error, '停止学习');
    }
  }

  /**
   * 自动停止学习 - 活动超时触发
   */
  async stopLearningByActivityTimeout() {
    try {
      console.log('SignalManager: 用户长时间无活动，自动停止学习');

      // 调用共用的清理方法
      await this.stopLearningAndCleanup();

      // 自动停止的错误提示
      this.handleError(new Error('长时间无操作，学习已自动停止'), '活动超时');

    } catch (error) {
      console.error('SignalManager: 活动超时停止失败:', error);
      this.handleError(error, '活动超时');
    }
  }

  /**
   * 自动停止学习 - 学习超时触发
   */
  async stopLearningByTimeout() {
    try {
      console.log('SignalManager: 学习超时，自动停止');

      // 调用共用的清理方法
      await this.stopLearningAndCleanup();

      // 自动停止的错误提示
      this.handleError(new Error('学习超时，20秒内未检测到信号'), '学习超时');

    } catch (error) {
      console.error('SignalManager: 学习超时停止失败:', error);
      this.handleError(error, '学习超时');
    }
  }

  /**
   * 停止学习并清理状态
   */
  async stopLearningAndCleanup() {
    try {
      // 清理定时器
      this.clearLearningTimers();

      // 发送停止指令到ESP32
      await this.sendLearningCommand('stop');

      // 恢复暂停的任务
      await this.resumeAllTasks();

      // 重置学习状态
      this.resetLearningState();

      // 更新UI
      this.hideLearningIndicator();
      this.updateLearningUI();

      // 关闭可能存在的保存模态框
      if (window.R1System?.hideModal) {
        window.R1System.hideModal();
      }

      // 清理本地存储
      localStorage.removeItem('signalManager_unsavedSignal');
      localStorage.removeItem('signalManager_pausedTasks');

      // 发布学习停止事件
      this.emitEvent('signal.learning.stopped', {
        timestamp: Date.now()
      });

      console.log('SignalManager: 学习状态已清理');

    } catch (error) {
      console.error('SignalManager: 清理学习状态失败:', error);
    }
  }

  /**
   * 暂停所有正在运行的任务
   */
  async pauseAllTasks() {
    try {
      console.log('SignalManager: 暂停所有任务');

      // 通过事件查询和暂停其他模块 - 符合解耦原则
      const pausedTasks = [];

      // 发布暂停请求事件，让各模块自行响应
      console.log('🎓 SignalManager: 发送控制模块暂停请求');
      this.emitEvent('control.pause.request', {
        source: 'SignalLearning',
        reason: '信号学习模式启动'
      });
      console.log('🎓 SignalManager: 控制模块暂停请求已发送');

      // 发布定时器暂停请求
      this.emitEvent('timer.pause.request', { source: 'SignalLearning' });

      // 发布控制任务暂停请求
      this.emitEvent('control.pause.request', { source: 'SignalLearning' });

      // 记录暂停的任务（用于后续恢复）
      pausedTasks.push(
        { module: 'timerSettings', state: 'paused' },
        { module: 'controlModule', state: 'paused' }
      );

      // 保存暂停的任务状态
      this.learningState.pausedTasks = pausedTasks;
      localStorage.setItem('signalManager_pausedTasks', JSON.stringify(pausedTasks));

      console.log('SignalManager: 已暂停任务:', pausedTasks);

    } catch (error) {
      console.error('SignalManager: 暂停任务失败:', error);
    }
  }

  /**
   * 恢复所有暂停的任务
   */
  async resumeAllTasks() {
    try {
      console.log('SignalManager: 恢复暂停的任务');

      // 发布系统恢复事件 - 符合解耦原则
      this.emitEvent('system.resume.request', {
        source: 'SignalLearning',
        reason: '信号学习模式结束'
      });

      // 发布具体的恢复请求
      this.emitEvent('timer.resume.request', { source: 'SignalLearning' });
      this.emitEvent('control.resume.request', { source: 'SignalLearning' });

      // 清空暂停任务列表
      this.learningState.pausedTasks = [];
      localStorage.removeItem('signalManager_pausedTasks');

      console.log('SignalManager: 任务恢复完成');

    } catch (error) {
      console.error('SignalManager: 恢复任务失败:', error);
    }
  }

  /**
   * 启动学习检测流程 - 统一的检测逻辑
   */
  startLearningDetection() {
    console.log('SignalManager: 启动学习检测流程');

    // 先清理之前的信号检测定时器，防止重复启动
    if (this.signalDetectionTimeout) {
      clearTimeout(this.signalDetectionTimeout);
      this.signalDetectionTimeout = null;
      console.log('SignalManager: 清理了之前的信号检测定时器');
    }

    // 启动活动检查
    this.startActivityCheck();

    // 启动学习超时检查
    this.startLearningTimeout();

    // 开始模拟信号检测 (2-3秒后)
    const detectionDelay = 3000; // 3秒固定延迟，实际应由后端控制
    window.UnifiedTimerManager.addTimer(
      'signal_detection_timeout',
      async () => {
        if (this.learningState.isLearning && !this.learningState.hasUnsavedSignal) {
          console.log('SignalManager: 开始模拟信号检测...');
          // 信号检测应通过后端API实现
          const signalData = await this.requestSignalDetection();
          if (signalData && this.learningState.isLearning) {
            await this.handleSignalDetected(signalData);
          }
        }
      },
      detectionDelay,
      false
    );

    console.log(`SignalManager: 信号检测将在 ${Math.round(detectionDelay/1000)} 秒后开始`);
  }

  /**
   * 启动学习超时检查
   */
  startLearningTimeout() {
    // 先清理之前的学习超时定时器，防止重复启动
    if (window.UnifiedTimerManager.hasTimer('learning_timeout')) {
      window.UnifiedTimerManager.removeTimer('learning_timeout');
      console.log('SignalManager: 清理了之前的学习超时定时器');
    }

    // 20秒无操作超时 - 只在没有检测到信号时触发
    window.UnifiedTimerManager.addTimer(
      'learning_timeout',
      () => {
        // 检查是否仍在学习状态
        if (!this.learningState.isLearning) {
          console.log('SignalManager: 学习已停止，跳过学习超时');
          return;
        }

        // 检查是否已经检测到信号
        if (this.learningState.hasUnsavedSignal) {
          console.log('SignalManager: 已检测到信号，跳过学习超时');
          return;
        }

        this.stopLearningByTimeout();
      },
      20000,
      false
    );

    console.log('SignalManager: 学习超时检查已启动 (20秒)');
  }

  /**
   * 启动活动检查
   */
  startActivityCheck() {
    // 只有在学习状态下才启动活动检查
    if (!this.learningState.isLearning) {
      console.log('SignalManager: 学习未启动，跳过活动检查');
      return;
    }

    // 先清理之前的活动检查定时器，防止重复启动
    if (window.UnifiedTimerManager.hasTimer('activity_check')) {
      window.UnifiedTimerManager.removeTimer('activity_check');
      console.log('SignalManager: 清理了之前的活动检查定时器');
    }

    this.updateActivityTime();

    // 每5秒检查一次用户活动
    window.UnifiedTimerManager.addTimer(
      'activity_check',
      () => {
        const now = Date.now();
        const timeSinceLastActivity = now - this.learningState.lastActivityTime;

        // 如果20秒无活动，自动停止学习
        if (timeSinceLastActivity > 20000) {
          this.stopLearningByActivityTimeout();
        }
      },
      5000,
      true // 重复执行
    );

    console.log('SignalManager: 活动检查已启动');
  }

  /**
   * 更新活动时间
   */
  updateActivityTime() {
    this.learningState.lastActivityTime = Date.now();
  }

  /**
   * 清理所有定时器
   */
  clearLearningTimers() {
    // 清理学习相关的所有定时器
    const learningTimers = [
      'learning_timeout',
      'signal_detection_timeout',
      'auto_save_timeout',
      'activity_check',
      'countdown_display'
    ];

    learningTimers.forEach(timerId => {
      if (window.UnifiedTimerManager.hasTimer(timerId)) {
        window.UnifiedTimerManager.removeTimer(timerId);
      }
    });

    console.log('SignalManager: 所有学习定时器已清理');
  }

  /**
   * 重置学习状态
   */
  resetLearningState() {
    this.learningState.isLearning = false;
    this.learningState.hasUnsavedSignal = false;
    this.learningState.currentSignalData = null;
    this.learningState.learningStartTime = 0;
    this.learningState.lastActivityTime = 0;
    this.learningState.pausedTasks = [];



    console.log('SignalManager: 学习状态已重置');
  }

  /**
   * 发送学习指令到ESP32
   */
  async sendLearningCommand(command) {
    try {
      if (!this.esp32) {
        console.warn('SignalManager: ESP32通信器未初始化，跳过指令发送（离线模式）');
        return { success: true, offline: true };
      }

      const response = await this.requestESP32('/api/learning', {
        method: 'POST',
        body: JSON.stringify({
          command: command,
          timestamp: Date.now()
        })
      });

      console.log(`SignalManager: 学习指令 "${command}" 发送成功:`, response);
      return response;

    } catch (error) {
      console.warn(`SignalManager: 学习指令 "${command}" 发送失败（离线模式）:`, error.message);
      // 不抛出错误，允许离线模式运行
      return { success: false, offline: true, error: error.message };
    }
  }



  // 模拟数据生成方法已移除 - 数据应来自后端

  // 模拟学习过程已移除 - 学习应通过后端API实现

  /**
   * 显示学习指示器
   */
  showLearningIndicator() {
    let indicator = $('#learningIndicator');
    if (!indicator) {
      // 创建学习指示器
      indicator = document.createElement('div');
      indicator.id = 'learningIndicator';
      indicator.className = 'learning-indicator';
      indicator.innerHTML = `
        <div class="learning-content">
          <h3>🔍 正在学习信号</h3>
          <p>请对准设备按下遥控器按钮</p>
          <div class="learning-spinner">
            <div class="spinner"></div>
          </div>
          <button class="cancel-learning-btn" data-action="cancel-learning">
            停止学习
          </button>
        </div>
      `;
      document.body.appendChild(indicator);

      // 添加事件监听器
      const cancelBtn = indicator.querySelector('.cancel-learning-btn');
      if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
          this.stopLearning();
        });
      }
    }
    indicator.style.display = 'flex';
  }

  /**
   * 隐藏学习指示器
   */
  hideLearningIndicator() {
    const indicator = $('#learningIndicator');
    if (indicator) {
      indicator.style.display = 'none';
    }
  }

  /**
   * 更新学习UI状态
   */
  updateLearningUI() {
    const learnBtn = $('#learnSignalBtn');
    const learnBtnIcon = $('#learnBtnIcon');
    const learnBtnText = $('#learnBtnText');

    if (learnBtn && learnBtnIcon && learnBtnText) {
      if (this.learningState.isLearning) {
        // 学习中状态 - 显示停止按钮
        learnBtnIcon.textContent = '⏹️';
        learnBtnText.textContent = '停止学习';
        learnBtn.classList.remove('primary');
        learnBtn.classList.add('danger');
        learnBtn.title = '点击停止学习信号';
      } else {
        // 未学习状态 - 显示学习按钮
        learnBtnIcon.textContent = '🔍';
        learnBtnText.textContent = '学习信号';
        learnBtn.classList.remove('danger');
        learnBtn.classList.add('primary');
        learnBtn.title = '点击开始学习信号';
      }
    }
  }

  /**
   * 保存未保存的信号到本地存储
   */
  saveUnsavedSignalToLocal() {
    try {
      if (this.learningState.currentSignalData) {
        localStorage.setItem('signalManager_unsavedSignal',
          JSON.stringify(this.learningState.currentSignalData));
        console.log('SignalManager: 未保存信号已保存到本地存储');
      }
    } catch (error) {
      console.error('SignalManager: 保存未保存信号到本地存储失败:', error);
    }
  }

  /**
   * 生成自动保存名称
   */
  generateAutoSaveName(dataType = 'signal') {
    const now = new Date();
    const timestamp = now.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).replace(/[\/\s:]/g, '_');

    if (dataType === 'signal') {
      // 标准格式：自动保存_signal_时间戳
      return `自动保存_signal_${timestamp}`;
    } else {
      // 原始数据格式：自动保存_rawdata_时间戳
      return `自动保存_rawdata_${timestamp}`;
    }
  }

  /**
   * 自动保存当前信号
   */
  autoSaveCurrentSignal() {
    try {
      if (!this.learningState.currentSignalData) {
        console.warn('SignalManager: 没有可自动保存的信号数据');
        return;
      }

      const signalData = this.learningState.currentSignalData;

      // 根据解析状态判断保存格式
      const isStandardFormat = signalData.parseSuccess;
      const dataType = isStandardFormat ? 'signal' : 'rawdata';
      const defaultName = this.generateAutoSaveName(dataType);

      // 创建信号对象
      const signal = {
        id: R1Utils.generateId('signal'),
        name: defaultName,
        type: 'other', // 默认类型为'other'
        description: `自动保存的${isStandardFormat ? '信号' : '原始数据'}`,
        dataType: dataType, // 标记数据类型
        isLearned: true,
        autoSaved: true,
        autoSaveReason: 'timeout',
        parseSuccess: signalData.parseSuccess,
        created: Date.now(),
        lastSent: null,
        sentCount: 0
      };

      // 根据解析状态设置字段
      if (isStandardFormat) {
        // 标准格式（解析成功）
        signal.signalCode = signalData.signalCode;
        signal.protocol = signalData.protocol;
        signal.frequency = signalData.frequency || '38000';
        signal.rawData = signalData.rawData || '';
      } else {
        // 原始数据格式（解析失败）
        signal.signalCode = 'RAW_DATA';
        signal.protocol = 'RAW';
        signal.frequency = signalData.frequency || '38000';
        signal.rawData = signalData.rawData || '';
      }

      // 保存信号 - 应通过后端API
      this.signals.set(signal.id, signal);
      // 本地存储已移除 - 数据应保存到后端

      // 发送信号添加事件
      this.emitEvent('signal.added', {
        signal: signal,
        timestamp: Date.now()
      });

      // 清理状态
      this.learningState.hasUnsavedSignal = false;
      this.learningState.currentSignalData = null;

      // 清理本地存储
      localStorage.removeItem('signalManager_unsavedSignal');

      // 关闭模态框
      if (window.R1System?.hideModal) {
        window.R1System.hideModal();
      }

      // 更新UI
      this.renderSignals();
      this.updateStats();

      // 停止学习（自动保存后完全退出学习状态）
      this.stopLearningAndCleanup();

      this.handleSuccess(`信号已自动保存为 "${defaultName}"，学习模式已退出`, '自动保存');

      console.log('SignalManager: 信号自动保存完成:', signal);

    } catch (error) {
      console.error('SignalManager: 自动保存失败:', error);
      this.handleError(error, '自动保存');
    }
  }

  // 模拟原始数据生成已移除 - 数据应来自后端

  /**
   * 保存学习到的信号
   */
  saveLearnedSignal() {
    try {
      // 更新活动时间
      this.updateActivityTime();

      // 获取表单数据
      const name = $('#learnedSignalName')?.value?.trim();
      const type = $('#learnedSignalType')?.value || 'other';
      const description = $('#learnedSignalDescription')?.value?.trim();

      if (!name) {
        this.handleError(new Error('信号名称不能为空'), '保存信号');
        // 不清除定时器，让用户有机会继续编辑
        return;
      }

      // 验证通过后才清除定时器
      // 清除自动保存定时器
      if (this.autoSaveTimeout) {
        clearTimeout(this.autoSaveTimeout);
        this.autoSaveTimeout = null;
      }

      // 清除倒计时
      if (this.countdownInterval) {
        clearInterval(this.countdownInterval);
        this.countdownInterval = null;
      }

      if (!this.learningState.currentSignalData) {
        this.handleError(new Error('没有可保存的信号数据，学习状态可能已退出'), '保存信号');
        // 关闭模态框，因为已经没有有效数据
        window.R1System.hideModal();
        return;
      }

      // 检查学习状态是否仍然有效
      if (!this.learningState.isLearning && !this.learningState.hasUnsavedSignal) {
        this.handleError(new Error('学习状态已退出，无法保存信号'), '保存信号');
        // 关闭模态框
        window.R1System.hideModal();
        return;
      }

      const signalData = this.learningState.currentSignalData;

      // 根据解析状态自动决定保存格式
      const isStandardFormat = signalData.parseSuccess;
      const dataType = isStandardFormat ? 'standard' : 'rawdata';

      // 创建信号对象
      const signal = {
        id: R1Utils.generateId('signal'),
        name: name,
        type: type,
        description: description,
        dataType: dataType, // 标记数据类型
        isLearned: true,
        autoSaved: false,
        parseSuccess: signalData.parseSuccess,
        created: Date.now(),
        lastSent: null,
        sentCount: 0
      };

      // 根据解析状态设置字段
      if (isStandardFormat) {
        // 标准格式（解析成功）
        signal.signalCode = signalData.signalCode;
        signal.protocol = signalData.protocol;
        signal.frequency = signalData.frequency || '38000';
        signal.rawData = signalData.rawData || '';
      } else {
        // 原始数据格式（解析失败）
        signal.signalCode = 'RAW_DATA';
        signal.protocol = 'RAW';
        signal.frequency = signalData.frequency || '38000';
        signal.rawData = signalData.rawData || '';
      }

      // 保存信号 - 应通过后端API
      this.signals.set(signal.id, signal);
      // 本地存储已移除 - 数据应保存到后端

      // 发送信号添加事件
      this.emitEvent('signal.added', {
        signal: signal,
        timestamp: Date.now()
      });

      // 清理状态
      this.learningState.hasUnsavedSignal = false;
      this.learningState.currentSignalData = null;

      // 清理本地存储
      localStorage.removeItem('signalManager_unsavedSignal');

      // 关闭模态框
      window.R1System.hideModal();

      // 更新UI
      this.renderSignals();
      this.updateStats();

      // 检查是否仍在学习状态，如果是则继续学习
      if (this.learningState.isLearning) {
        console.log('SignalManager: 信号保存完成，继续学习模式');
        // 重新显示学习指示器，继续学习
        this.showLearningIndicator();

        // 重新启动完整的学习检测流程
        this.startLearningDetection();

        this.handleSuccess(`信号 "${name}" 保存成功 (${isStandardFormat ? '标准格式' : '原始数据格式'})，继续学习模式`, '信号保存');
      } else {
        console.log('SignalManager: 学习已停止，恢复暂停的任务');
        // 学习结束，恢复暂停的任务
        this.resumeAllTasks();
        this.handleSuccess(`信号 "${name}" 保存成功 (${isStandardFormat ? '标准格式' : '原始数据格式'})`, '信号保存');
      }

      console.log('SignalManager: 信号保存完成:', signal);

    } catch (error) {
      console.error('SignalManager: 保存信号失败:', error);
      this.handleError(error, '信号保存');
    }
  }

  /**
   * 取消学习到的信号
   */
  cancelLearnedSignal() {
    try {
      console.log('SignalManager: 用户取消信号保存');

      // 清除定时器
      if (this.autoSaveTimeout) {
        clearTimeout(this.autoSaveTimeout);
        this.autoSaveTimeout = null;
      }

      if (this.countdownInterval) {
        clearInterval(this.countdownInterval);
        this.countdownInterval = null;
      }

      // 清理状态
      this.learningState.hasUnsavedSignal = false;
      this.learningState.currentSignalData = null;

      // 清理本地存储
      localStorage.removeItem('signalManager_unsavedSignal');

      // 关闭模态框
      window.R1System.hideModal();

      // 检查是否仍在学习状态，如果是则继续学习
      if (this.learningState.isLearning) {
        console.log('SignalManager: 继续学习模式');
        // 重新显示学习指示器，继续学习
        this.showLearningIndicator();

        // 重新启动完整的学习检测流程
        this.startLearningDetection();

        this.handleSuccess('已取消信号保存，继续学习模式', '取消保存');
      } else {
        console.log('SignalManager: 学习已停止，恢复暂停的任务');
        // 学习结束，恢复暂停的任务
        this.resumeAllTasks();
        this.handleSuccess('已取消信号保存，学习模式已退出', '取消保存');
      }

    } catch (error) {
      console.error('SignalManager: 取消信号保存失败:', error);
      this.handleError(error, '取消保存');
    }
  }



  /**
   * 恢复未保存的信号
   */
  recoverUnsavedSignal() {
    try {
      const unsavedSignal = localStorage.getItem('signalManager_unsavedSignal');
      if (!unsavedSignal) {
        this.handleError(new Error('没有找到未保存的信号数据'), '信号恢复');
        return;
      }

      const signalData = JSON.parse(unsavedSignal);

      console.log('SignalManager: 开始恢复未保存信号，触发学习暂停:', signalData);

      // 关闭恢复对话框
      window.R1System.hideModal();

      // 重要：恢复信号时也需要触发学习暂停流程
      this.pauseAllTasks();

      // 设置学习状态（模拟学习模式）
      this.learningState.isLearning = true;
      this.learningState.currentSignalData = signalData;
      this.learningState.hasUnsavedSignal = true;
      this.learningState.learningStartTime = Date.now();
      this.learningState.lastActivityTime = Date.now();

      // 更新学习按钮状态
      this.updateLearningUI();

      // 显示编辑对话框
      this.showSignalEditDialog(signalData);

      console.log('SignalManager: 未保存信号已恢复，学习暂停已触发');

    } catch (error) {
      console.error('SignalManager: 恢复信号失败:', error);
      this.handleError(error, '信号恢复');
      localStorage.removeItem('signalManager_unsavedSignal');
    }
  }

  /**
   * 丢弃恢复的信号
   */
  discardRecoverySignal() {
    try {
      // 清理本地存储
      localStorage.removeItem('signalManager_unsavedSignal');

      // 关闭对话框
      window.R1System.hideModal();

      this.handleSuccess('未保存的信号已丢弃', '丢弃信号');

      console.log('SignalManager: 未保存信号已丢弃');

    } catch (error) {
      console.error('SignalManager: 丢弃信号失败:', error);
      this.handleError(error, '丢弃信号');
    }
  }

  /**
   * 请求信号检测 - 通过后端API
   */
  async requestSignalDetection() {
    try {
      // 检查是否仍在学习状态
      if (!this.learningState.isLearning) {
        return null;
      }

      console.log('SignalManager: 请求后端进行信号检测...');

      // 通过ESP32 API请求信号检测
      const response = await this.requestESP32('/api/learning/detect', {
        method: 'POST',
        body: JSON.stringify({
          timeout: 30000,
          timestamp: Date.now()
        })
      });

      if (response.success && response.data) {
        console.log('SignalManager: 后端信号检测成功:', response.data);
        return response.data;
      } else {
        console.warn('SignalManager: 后端信号检测失败:', response.error);
        return null;
      }

    } catch (error) {
      console.error('SignalManager: 信号检测请求失败:', error);
      return null;
    }
  }

  // 模拟数据生成方法已移除 - 数据应来自后端API

  /**
   * 处理检测到的信号
   */
  async handleSignalDetected(signalData) {
    try {
      // 检查是否已有未保存的信号
      if (this.learningState.hasUnsavedSignal) {
        this.handleError(new Error('请先保存当前信号再学习新信号'), '信号检测');
        return;
      }

      console.log('SignalManager: 处理检测到的信号:', signalData);

      // 更新活动时间
      this.updateActivityTime();

      // 清除学习超时定时器（因为已经检测到信号）
      if (this.learningTimeout) {
        clearTimeout(this.learningTimeout);
        this.learningTimeout = null;
        console.log('SignalManager: 已清除学习超时定时器（检测到信号）');
      }

      // 隐藏学习指示器（重要：避免窗口叠加）
      this.hideLearningIndicator();
      console.log('SignalManager: 学习指示器已隐藏');

      // 设置当前信号数据
      this.learningState.currentSignalData = signalData;
      this.learningState.hasUnsavedSignal = true;

      // 保存到本地存储（防止页面刷新丢失）
      this.saveUnsavedSignalToLocal();

      // 显示编辑对话框
      this.showSignalEditDialog(signalData);

      // 启动自动保存定时器
      this.startAutoSaveTimer();

      // 发布信号检测事件
      this.emitEvent('signal.detected', {
        signalData: signalData,
        timestamp: Date.now()
      });

    } catch (error) {
      console.error('SignalManager: 处理信号检测失败:', error);
      this.handleError(error, '信号处理');
    }
  }

  /**
   * 显示信号编辑对话框
   */
  showSignalEditDialog(signalData) {
    const modalContent = `
      <div class="modal-header">
        <h3>保存学习到的信号</h3>
        <div class="learning-countdown" id="learningCountdown">
          <span class="countdown-text">20秒后自动保存</span>
          <div class="countdown-bar">
            <div class="countdown-fill" id="countdownFill"></div>
          </div>
        </div>
      </div>
      <div class="modal-body">
        <div class="signal-preview">
          <h4>检测到的信号信息</h4>
          <div class="signal-data">
            ${signalData.parseSuccess ? `
              <div class="data-item">
                <label>解析状态:</label>
                <span class="data-value" style="color: #10b981;">✅ 解析成功</span>
              </div>
              <div class="data-item">
                <label>信号码:</label>
                <span class="data-value">${signalData.signalCode}</span>
              </div>
              <div class="data-item">
                <label>协议:</label>
                <span class="data-value">${signalData.protocol}</span>
              </div>
            ` : `
              <div class="data-item">
                <label>解析状态:</label>
                <span class="data-value" style="color: #f59e0b;">⚠️ 解析失败，将保存原始数据</span>
              </div>
              <div class="data-item">
                <label>协议:</label>
                <span class="data-value">未知协议</span>
              </div>
            `}
            <div class="data-item">
              <label>频率:</label>
              <span class="data-value">${signalData.frequency} Hz</span>
            </div>
            <div class="data-item">
              <label>检测时间:</label>
              <span class="data-value">${new Date(signalData.detectedAt).toLocaleString()}</span>
            </div>
            <div class="data-item">
              <label>原始数据:</label>
              <span class="data-value">${signalData.rawData?.substring(0, 30)}...</span>
            </div>
          </div>
        </div>

        <form id="learnedSignalForm">
          <div class="form-group">
            <label for="learnedSignalName">信号名称 *</label>
            <input type="text" id="learnedSignalName" class="form-input"
                   placeholder="请输入信号名称" required>
          </div>
          <div class="form-group">
            <label for="learnedSignalType">信号类型</label>
            <select id="learnedSignalType" class="form-input">
              <option value="tv">电视</option>
              <option value="ac">空调</option>
              <option value="fan">风扇</option>
              <option value="light">灯光</option>
              <option value="other" selected>其他</option>
            </select>
          </div>
          <div class="form-group">
            <label for="learnedSignalDescription">描述</label>
            <textarea id="learnedSignalDescription" class="form-input" rows="3"
                      placeholder="请输入信号描述（可选）"></textarea>
          </div>
          <div class="form-group">
            <label>保存说明</label>
            <div class="save-info">
              ${signalData.parseSuccess ?
                '<p style="color: #10b981;">✅ 信号已成功解析，将保存为标准格式</p>' :
                '<p style="color: #f59e0b;">⚠️ 信号解析失败，将保存为原始数据格式</p>'
              }
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <div class="form-actions">
          <button type="button" class="btn secondary" id="cancelLearnedSignalBtn">取消</button>
          <button type="button" class="btn primary" id="saveLearnedSignalBtn">保存信号</button>
        </div>
      </div>
    `;

    console.log('SignalManager: 准备显示模态框...');
    window.R1System.showModal(modalContent);
    console.log('SignalManager: 模态框显示命令已发送');

    // 验证模态框是否显示
    setTimeout(() => {
      const modalOverlay = document.getElementById('modalOverlay');
      if (modalOverlay) {
        console.log('SignalManager: 模态框状态检查:', {
          display: modalOverlay.style.display,
          opacity: modalOverlay.style.opacity,
          visible: modalOverlay.offsetHeight > 0
        });
      } else {
        console.error('SignalManager: 找不到模态框元素!');
      }
    }, 100);

    // 延迟添加事件监听器，确保DOM元素已渲染
    setTimeout(() => {
      this.attachModalEventListeners();
    }, 100);

    // 启动倒计时显示
    this.startCountdownDisplay();
  }

  /**
   * 为模态框添加事件监听器
   */
  attachModalEventListeners() {
    console.log('SignalManager: 开始绑定模态框事件监听器...');

    // 取消按钮
    const cancelBtn = $('#cancelLearnedSignalBtn');
    console.log('SignalManager: 查找取消按钮:', cancelBtn ? '找到' : '未找到');
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => {
        console.log('SignalManager: 取消按钮被点击');
        this.cancelLearnedSignal();
      });
      console.log('SignalManager: 取消按钮事件监听器已绑定');
    }

    // 保存按钮
    const saveBtn = $('#saveLearnedSignalBtn');
    console.log('SignalManager: 查找保存按钮:', saveBtn ? '找到' : '未找到');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        console.log('SignalManager: 保存按钮被点击');
        this.saveLearnedSignal();
      });
      console.log('SignalManager: 保存按钮事件监听器已绑定');
    }

    // 输入框活动监听
    const nameInput = $('#learnedSignalName');
    if (nameInput) {
      nameInput.addEventListener('input', () => {
        this.updateActivityTime();
      });
      nameInput.focus();
    }

    const descInput = $('#learnedSignalDescription');
    if (descInput) {
      descInput.addEventListener('input', () => {
        this.updateActivityTime();
      });
    }

    // 如果按钮没有找到，输出调试信息
    if (!cancelBtn || !saveBtn) {
      console.error('SignalManager: 模态框按钮未找到，检查DOM结构...');
      const modalContent = $('#modalContent');
      if (modalContent) {
        console.log('SignalManager: 模态框内容:', modalContent.innerHTML);
      } else {
        console.error('SignalManager: 模态框容器未找到');
      }
    }
  }

  /**
   * 启动自动保存定时器
   */
  startAutoSaveTimer() {
    // 清除之前的定时器
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout);
    }

    // 20秒后自动保存
    this.autoSaveTimeout = setTimeout(() => {
      console.log('SignalManager: 自动保存超时，执行自动保存');
      this.autoSaveCurrentSignal();
    }, 20000);

    console.log('SignalManager: 自动保存定时器已启动 (20秒)');
  }

  /**
   * 启动倒计时显示
   */
  startCountdownDisplay() {
    const countdownText = document.querySelector('#learningCountdown .countdown-text');
    const countdownFill = $('#countdownFill');

    if (!countdownText || !countdownFill) return;

    let timeLeft = 20;

    // 使用统一定时器管理器
    window.UnifiedTimerManager.addTimer(
      'countdown_display',
      () => {
        timeLeft--;

        if (countdownText) {
          countdownText.textContent = `${timeLeft}秒后自动保存`;
        }

        if (countdownFill) {
          const percentage = ((20 - timeLeft) / 20) * 100;
          countdownFill.style.width = `${percentage}%`;
        }

        if (timeLeft <= 0) {
          window.UnifiedTimerManager.removeTimer('countdown_display');
        }
      },
      1000,
      true // 重复执行
    );
  }

  /**
   * 初始化文件导入处理器
   */
  initFileImportHandlers() {
    const fileInput = $('#importFileInput');
    const confirmBtn = $('#confirmFileImport');
    const previewArea = $('#importPreview');
    const previewContent = $('#previewContent');

    if (!fileInput || !confirmBtn) return;

    fileInput.addEventListener('change', async (e) => {
      const file = e.target.files[0];
      if (!file) {
        previewArea.style.display = 'none';
        confirmBtn.disabled = true;
        return;
      }

      try {
        const content = await this.readFileContent(file);
        const parsedData = await this.parseImportFile(content, file.type);

        if (parsedData && parsedData.length > 0) {
          this.displayImportPreview(parsedData, previewContent);
          previewArea.style.display = 'block';
          confirmBtn.disabled = false;

          // 绑定确认导入事件
          confirmBtn.onclick = () => this.executeFileImport(parsedData);
        } else {
          throw new Error('文件中没有找到有效的信号数据');
        }
      } catch (error) {
        this.handleError(error, '文件解析');
        previewArea.style.display = 'none';
        confirmBtn.disabled = true;
      }
    });
  }

  /**
   * 初始化文本导入处理器
   */
  initTextImportHandlers() {
    const textInput = $('#importTextInput');
    const previewBtn = $('#previewTextImport');
    const confirmBtn = $('#confirmTextImport');
    const previewArea = $('#textImportPreview');
    const previewContent = $('#textPreviewContent');

    if (!textInput || !previewBtn || !confirmBtn) return;

    previewBtn.addEventListener('click', () => {
      const textContent = textInput.value.trim();
      if (!textContent) {
        this.handleError(new Error('请输入信号数据'), '文本解析');
        return;
      }

      try {
        const parsedData = this.parseTextImport(textContent);
        if (parsedData && parsedData.length > 0) {
          this.displayImportPreview(parsedData, previewContent);
          previewArea.style.display = 'block';
          confirmBtn.disabled = false;

          // 绑定确认导入事件
          confirmBtn.onclick = () => this.executeTextImport(parsedData);
        } else {
          throw new Error('没有找到有效的信号数据');
        }
      } catch (error) {
        this.handleError(error, '文本解析');
        previewArea.style.display = 'none';
        confirmBtn.disabled = true;
      }
    });
  }

  /**
   * 读取文件内容
   */
  readFileContent(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsText(file, 'UTF-8');
    });
  }

  /**
   * 解析导入文件
   */
  async parseImportFile(content, fileType) {
    try {
      if (fileType.includes('json') || content.trim().startsWith('{')) {
        // JSON格式 - 增强解析逻辑
        const jsonData = JSON.parse(content);
        console.log('🔍 JSON解析结果:', jsonData);

        // 尝试多种可能的JSON结构
        let signalsArray = null;

        if (jsonData.signals && Array.isArray(jsonData.signals)) {
          signalsArray = jsonData.signals;
        } else if (jsonData.testSignals && Array.isArray(jsonData.testSignals)) {
          signalsArray = jsonData.testSignals;
        } else if (Array.isArray(jsonData)) {
          signalsArray = jsonData;
        } else if (typeof jsonData === 'object') {
          // 如果是单个对象，转换为数组
          signalsArray = [jsonData];
        } else {
          throw new Error('JSON格式不正确：找不到信号数组');
        }

        console.log(`🔍 找到 ${signalsArray.length} 个信号数据`);
        return signalsArray;
      } else {
        // 文本格式，按行解析
        return this.parseTextImport(content);
      }
    } catch (error) {
      console.error('❌ 文件解析错误:', error);
      throw new Error(`文件解析失败: ${error.message}`);
    }
  }

  /**
   * 解析文本导入数据 - 增强版，支持多种格式
   */
  parseTextImport(textContent) {
    const lines = textContent.split('\n').filter(line => line.trim());
    const parsedSignals = [];
    const defaultType = $('#defaultSignalType')?.value || 'other';

    console.log(`🔍 开始解析文本，共 ${lines.length} 行`);

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line || line.startsWith('#') || line.startsWith('##') || line.startsWith('|')) {
        continue; // 跳过空行、注释和表格分隔符
      }

      try {
        // 尝试解析JSON单行格式
        if (line.startsWith('{') && line.endsWith('}')) {
          const jsonSignal = JSON.parse(line);
          const convertedSignal = this.smartConvertSignalData(jsonSignal);
          if (convertedSignal.signalCode) {
            parsedSignals.push(convertedSignal);
            console.log(`✅ JSON格式解析成功: ${convertedSignal.name || '未知'}`);
            continue;
          }
        }

        // 尝试解析CSV格式
        if (line.includes(',')) {
          const parts = line.split(',').map(part => part.trim());
          if (parts.length >= 3) {
            const [signalCode, protocol, name, description = ''] = parts;

            if (this.validateSignalCode(signalCode) && this.validateProtocol(protocol)) {
              parsedSignals.push({
                signalCode: signalCode,
                protocol: protocol.toUpperCase(),
                name: name,
                description: description,
                type: defaultType,
                isLearned: false,
                frequency: '38000',
                created: Date.now(),
                lastSent: null,
                sentCount: 0
              });
              console.log(`✅ CSV格式解析成功: ${name}`);
              continue;
            }
          }
        }

        // 尝试解析管道分隔符格式
        if (line.includes('|')) {
          const parts = line.split('|').map(part => part.trim());
          if (parts.length >= 3) {
            const [name, signalCode, protocol] = parts;

            if (this.validateSignalCode(signalCode) && this.validateProtocol(protocol)) {
              parsedSignals.push({
                signalCode: signalCode,
                protocol: protocol.toUpperCase(),
                name: name,
                description: parts[6] || '',
                type: parts[4] || defaultType,
                isLearned: parts[5] === 'true' || parts[5] === '是',
                frequency: parts[3] || '38000',
                created: Date.now(),
                lastSent: null,
                sentCount: parseInt(parts[7]) || 0
              });
              console.log(`✅ 管道分隔符格式解析成功: ${name}`);
              continue;
            }
          }
        }

        // 尝试解析键值对格式
        if (line.includes(':') && !line.includes('0x')) {
          // 这可能是键值对格式的一部分，需要收集多行
          continue;
        }

        console.warn(`⚠️ 第${i + 1}行无法解析，跳过: ${line}`);
      } catch (error) {
        console.warn(`❌ 第${i + 1}行解析失败，跳过: ${line}`, error);
      }
    }

    console.log(`🔍 文本解析完成，成功解析 ${parsedSignals.length} 个信号`);
    return parsedSignals;
  }

  /**
   * 验证信号码格式
   */
  validateSignalCode(signalCode) {
    // 支持的信号码格式：0x开头的16进制
    const hexPattern = /^0x[0-9A-Fa-f]+$/;
    return hexPattern.test(signalCode);
  }

  /**
   * 验证协议类型
   */
  validateProtocol(protocol) {
    const validProtocols = ['NEC', 'RC5', 'SONY', 'RAW', 'OTHER'];
    return validProtocols.includes(protocol.toUpperCase());
  }

  /**
   * 显示导入预览
   */
  displayImportPreview(signals, container) {
    if (!container) return;

    const previewHTML = `
      <div class="import-summary">
        <p>将导入 <strong>${signals.length}</strong> 个信号</p>
      </div>
      <div class="import-signals-list">
        ${signals.map((signal) => `
          <div class="import-signal-item">
            <div class="signal-info">
              <div class="signal-code">${signal.signalCode}</div>
              <div class="signal-details">
                <span class="signal-name">${signal.name}</span>
                <span class="signal-protocol">${signal.protocol}</span>
              </div>
            </div>
            <div class="signal-description">${signal.description || '无描述'}</div>
          </div>
        `).join('')}
      </div>
    `;

    container.innerHTML = previewHTML;
  }

  /**
   * 执行文件导入
   */
  async executeFileImport(signals) {
    try {
      let successCount = 0;
      let skipCount = 0;

      // 🚨 文件导入前状态检查
      console.log(`🚨 === 文件导入开始调试 ===`);
      console.log(`📊 导入前信号总数: ${this.signals.size}`);
      console.log(`📥 准备导入信号数量: ${signals.length}`);
      console.log(`📋 导入前现有信号列表:`);
      Array.from(this.signals.values()).forEach((signal, index) => {
        console.log(`  ${index + 1}. ${signal.name} (${signal.signalCode}) - ID: ${signal.id}`);
      });

      for (const signalData of signals) {
        console.log(`\n🔍 处理文件导入信号 ${successCount + skipCount + 1}:`, signalData);
        // 🚨 标准化数据后再检查重复
        const normalizedData = this.smartConvertSignalData(signalData);
        const standardSignal = this.createStandardSignal(normalizedData);

        if (!standardSignal) {
          skipCount++;
          console.warn(`❌ 信号创建失败（缺少信号码），跳过:`, signalData);
          continue;
        }

        // 检查信号码是否已存在 - 使用标准化后的数据
        const existingSignal = Array.from(this.signals.values())
          .find(s => s.signalCode === standardSignal.signalCode && s.protocol === standardSignal.protocol);

        console.log(`🔍 文件导入重复检查: "${standardSignal.name}" (${standardSignal.signalCode}, ${standardSignal.protocol})`);
        if (existingSignal) {
          skipCount++;
          console.warn(`信号已存在，跳过: ${standardSignal.name} (${standardSignal.signalCode}) - 与现有信号 "${existingSignal.name}" 重复`);
          continue;
        }

        // 验证信号格式
        if (!this.validateSignalFormat(standardSignal)) {
          skipCount++;
          console.warn(`❌ 信号格式验证失败，跳过:`, signalData);
          continue;
        }

        // 检查ID是否冲突 - 使用循环确保生成唯一ID
        console.log(`🆔 检查ID冲突: ${standardSignal.id}`);
        let attempts = 0;
        while (this.signals.has(standardSignal.id) && attempts < 10) {
          const existingSignalWithSameId = this.signals.get(standardSignal.id);
          console.warn(`⚠️ ID冲突! 现有信号: "${existingSignalWithSameId.name}" (${existingSignalWithSameId.signalCode})`);
          console.warn(`⚠️ 新信号: "${standardSignal.name}" (${standardSignal.signalCode})`);
          console.warn(`⚠️ 重新生成ID (尝试 ${attempts + 1}): ${standardSignal.id}`);

          // 生成新ID，使用时间戳确保唯一性
          const timestamp = Date.now();
          const counter = attempts + 1;
          standardSignal.id = `signal_${timestamp}_${counter}`;

          console.log(`🆔 新ID: ${standardSignal.id}`);
          attempts++;
        }

        if (attempts >= 10) {
          console.error(`❌ 无法生成唯一ID，跳过信号: ${standardSignal.name}`);
          continue;
        }

        if (attempts === 0) {
          console.log(`✅ ID无冲突，可以使用: ${standardSignal.id}`);
        } else {
          console.log(`✅ 经过 ${attempts} 次尝试，生成唯一ID: ${standardSignal.id}`);
        }

        // 添加信号前的最终检查
        console.log(`➕ 准备添加信号: ${standardSignal.name} (ID: ${standardSignal.id})`);
        console.log(`📊 添加前信号总数: ${this.signals.size}`);

        this.signals.set(standardSignal.id, standardSignal);

        console.log(`📊 添加后信号总数: ${this.signals.size}`);
        successCount++;
        console.log(`✅ 文件导入成功添加信号: ${standardSignal.name} (${standardSignal.signalCode})`);
        console.log(`📊 当前信号总数: ${this.signals.size}`);

        // 验证信号确实被添加
        const addedSignal = this.signals.get(standardSignal.id);
        if (addedSignal) {
          console.log(`✅ 验证: 信号已成功存储到Map中`);
        } else {
          console.error(`❌ 错误: 信号未能存储到Map中!`);
        }
      }

      // 🚨 文件导入后状态检查
      console.log(`\n🚨 === 文件导入完成调试 ===`);
      console.log(`📊 导入后信号总数: ${this.signals.size}`);
      console.log(`📋 导入后所有信号列表:`);
      Array.from(this.signals.values()).forEach((signal, index) => {
        console.log(`  ${index + 1}. ${signal.name} (${signal.signalCode}) - ID: ${signal.id}`);
      });

      // 本地存储已移除 - 数据应保存到后端

      // 更新UI
      this.renderSignals();
      this.updateStats();

      // 关闭模态框
      window.R1System.hideModal();

      // 显示结果
      const message = `文件导入完成: 成功 ${successCount} 个，跳过 ${skipCount} 个`;
      this.handleSuccess(message, '文件导入');

    } catch (error) {
      this.handleError(error, '文件导入');
    }
  }

  /**
   * 执行文本导入
   */
  async executeTextImport(signals) {
    try {
      let successCount = 0;
      let skipCount = 0;

      console.log(`🔍 开始导入 ${signals.length} 个信号`);
      console.log(`🔍 导入前信号总数: ${this.signals.size}`);

      // 🚨 导入前状态检查
      console.log(`🚨 === 导入开始调试 ===`);
      console.log(`📊 导入前信号总数: ${this.signals.size}`);
      console.log(`📥 准备导入信号数量: ${signals.length}`);
      console.log(`📋 导入前现有信号列表:`);
      Array.from(this.signals.values()).forEach((signal, index) => {
        console.log(`  ${index + 1}. ${signal.name} (${signal.signalCode}) - ID: ${signal.id}`);
      });

      for (const rawSignalData of signals) {
        console.log(`\n🔍 处理导入信号 ${successCount + skipCount + 1}:`, rawSignalData);

        // 智能转换任意格式到标准格式
        const convertedData = this.smartConvertSignalData(rawSignalData);
        const standardSignal = this.createStandardSignal(convertedData);

        if (!standardSignal) {
          skipCount++;
          console.warn(`❌ 信号创建失败（缺少信号码），跳过:`, rawSignalData);
          continue;
        }

        console.log(`🔄 转换后的标准信号:`, standardSignal);

        if (!this.validateSignalFormat(standardSignal)) {
          skipCount++;
          console.warn(`❌ 信号格式验证失败，跳过:`, rawSignalData);
          continue;
        }

        // 检查信号码是否已存在
        const existingSignal = Array.from(this.signals.values())
          .find(s => s.signalCode === standardSignal.signalCode && s.protocol === standardSignal.protocol);

        console.log(`🔍 重复检查: 信号码="${standardSignal.signalCode}", 协议="${standardSignal.protocol}"`);

        // 🚨 详细的重复检查调试
        console.log(`🔍 现有信号重复检查详情:`);
        Array.from(this.signals.values()).forEach((existing, index) => {
          const codeMatch = existing.signalCode === standardSignal.signalCode;
          const protocolMatch = existing.protocol === standardSignal.protocol;
          console.log(`  ${index + 1}. "${existing.name}"`);
          console.log(`     现有信号码: "${existing.signalCode}" vs 新信号码: "${standardSignal.signalCode}" = ${codeMatch}`);
          console.log(`     现有协议: "${existing.protocol}" vs 新协议: "${standardSignal.protocol}" = ${protocolMatch}`);
          console.log(`     是否重复: ${codeMatch && protocolMatch}`);
        });

        if (existingSignal) {
          skipCount++;
          console.warn(`❌ 信号已存在，跳过: ${standardSignal.name} (${standardSignal.signalCode}) - 与现有信号 "${existingSignal.name}" 重复`);
          continue;
        } else {
          console.log(`✅ 信号不重复，可以添加`);
        }

        // 检查ID是否冲突 - 使用循环确保生成唯一ID
        console.log(`🆔 文本导入检查ID冲突: ${standardSignal.id}`);
        let attempts = 0;
        while (this.signals.has(standardSignal.id) && attempts < 10) {
          const existingSignalWithSameId = this.signals.get(standardSignal.id);
          console.warn(`⚠️ 文本导入ID冲突! 现有信号: "${existingSignalWithSameId.name}" (${existingSignalWithSameId.signalCode})`);
          console.warn(`⚠️ 新信号: "${standardSignal.name}" (${standardSignal.signalCode})`);
          console.warn(`⚠️ 重新生成ID (尝试 ${attempts + 1}): ${standardSignal.id}`);

          // 生成新ID，使用时间戳确保唯一性
          const timestamp = Date.now();
          const counter = attempts + 1;
          standardSignal.id = `signal_${timestamp}_${counter}`;

          console.log(`🆔 新ID: ${standardSignal.id}`);
          attempts++;
        }

        if (attempts >= 10) {
          console.error(`❌ 文本导入无法生成唯一ID，跳过信号: ${standardSignal.name}`);
          continue;
        }

        if (attempts === 0) {
          console.log(`✅ 文本导入ID无冲突，可以使用: ${standardSignal.id}`);
        } else {
          console.log(`✅ 文本导入经过 ${attempts} 次尝试，生成唯一ID: ${standardSignal.id}`);
        }

        // 添加标准格式信号
        console.log(`➕ 准备添加信号: ${standardSignal.name} (ID: ${standardSignal.id})`);
        this.signals.set(standardSignal.id, standardSignal);
        successCount++;
        console.log(`✅ 成功添加标准格式信号: ${standardSignal.name} (ID: ${standardSignal.id})`);
        console.log(`📊 当前信号总数: ${this.signals.size}`);

        // 验证信号确实被添加
        const addedSignal = this.signals.get(standardSignal.id);
        if (addedSignal) {
          console.log(`✅ 验证: 信号已成功存储到Map中`);
        } else {
          console.error(`❌ 错误: 信号未能存储到Map中!`);
        }
      }

      // 🚨 导入后状态检查
      console.log(`\n🚨 === 导入完成调试 ===`);
      console.log(`📊 导入后信号总数: ${this.signals.size}`);
      console.log(`📋 导入后所有信号列表:`);
      Array.from(this.signals.values()).forEach((signal, index) => {
        console.log(`  ${index + 1}. ${signal.name} (${signal.signalCode}) - ID: ${signal.id}`);
      });

      // 🚨 保存前最终检查
      console.log(`\n🚨 === 保存前最终检查 ===`);
      console.log(`📊 保存前信号总数: ${this.signals.size}`);
      console.log(`📋 保存前信号列表:`);
      Array.from(this.signals.values()).forEach((signal, index) => {
        console.log(`  ${index + 1}. ${signal.name} (${signal.signalCode}) - ID: ${signal.id}`);
      });

      // 本地存储已移除 - 数据应保存到后端

      // 🚨 保存后检查
      console.log(`\n🚨 === 保存后检查 ===`);
      console.log(`📊 保存后信号总数: ${this.signals.size}`);

      // 更新UI
      this.renderSignals();
      this.updateStats();

      // 🚨 渲染后最终检查
      setTimeout(() => {
        console.log(`\n🚨 === 渲染后最终检查 ===`);
        console.log(`📊 渲染后信号总数: ${this.signals.size}`);
        const gridContainer = $('#signalsGrid');
        const listContainer = $('#signalsList');
        const gridCount = gridContainer ? gridContainer.children.length : 0;
        const listCount = listContainer ? listContainer.children.length : 0;
        console.log(`🎨 DOM元素数量: 网格=${gridCount}, 列表=${listCount}`);
        console.log(`🚨 === 导入调试结束 ===\n`);
      }, 100);

      // 关闭模态框
      window.R1System.hideModal();



      // 显示结果
      const message = `文本导入完成: 成功 ${successCount} 个，跳过 ${skipCount} 个`;
      this.handleSuccess(message, '文本导入');

    } catch (error) {
      this.handleError(error, '文本导入');
    }
  }

  // ==================== 事件处理方法 ====================

  /**
   * 处理信号请求（来自控制模块）
   */
  handleSignalRequest(data) {
    try {
      console.log(`📨 SignalManager: 收到信号请求 - 来源: ${data.source}, 目的: ${data.purpose}`);

      // 获取所有信号
      const allSignals = Array.from(this.signals.values());

      console.log(`📤 SignalManager: 返回 ${allSignals.length} 个信号`);

      // 调用回调函数返回信号
      if (data.callback && typeof data.callback === 'function') {
        console.log(`✅ SignalManager: 调用回调函数`);
        data.callback(allSignals);
      } else {
        console.error(`❌ SignalManager: 回调函数无效`, {
          hasCallback: !!data.callback,
          callbackType: typeof data.callback,
          dataKeys: Object.keys(data)
        });
      }

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '处理信号请求';

    } catch (error) {
      console.error('❌ SignalManager: 处理信号请求失败:', error);
      this.performance.errorCount++;

      // 即使出错也要调用回调，避免控制模块无限等待
      if (data.callback && typeof data.callback === 'function') {
        data.callback([]);
      }
    }
  }

  /**
   * 处理选中信号请求（来自控制模块）
   */
  handleSelectedSignalRequest(data) {
    try {
      console.log(`📨 SignalManager: 收到选中信号请求 - 来源: ${data.source}`);

      // 获取选中的信号
      const selectedSignals = Array.from(this.selectedSignals)
        .map(id => this.signals.get(id))
        .filter(signal => signal !== undefined);

      console.log(`📤 SignalManager: 返回 ${selectedSignals.length} 个选中信号`);

      // 调用回调函数返回信号
      if (data.callback && typeof data.callback === 'function') {
        data.callback(selectedSignals);
      }

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '处理选中信号请求';

    } catch (error) {
      console.error('❌ SignalManager: 处理选中信号请求失败:', error);
      this.performance.errorCount++;

      if (data.callback && typeof data.callback === 'function') {
        data.callback([]);
      }
    }
  }

  /**
   * 处理信号计数请求（来自状态显示模块）
   */
  handleSignalCountRequest(data) {
    try {
      console.log(`📨 SignalManager: 收到信号计数请求 - 来源: ${data.source || 'StatusDisplay'}`);

      const signalCount = this.signals.size;
      console.log(`📤 SignalManager: 返回信号数量: ${signalCount}`);

      // 通过回调返回结果
      if (data.callback && typeof data.callback === 'function') {
        data.callback(signalCount);
      }

      this.performance.operationCount++;
      this.performance.lastOperation = '处理信号计数请求';
    } catch (error) {
      this.performance.errorCount++;
      console.error('SignalManager: 处理信号计数请求失败:', error);
      if (data.callback && typeof data.callback === 'function') {
        data.callback(0);
      }
    }
  }

  /**
   * 处理按ID查询信号请求（来自控制模块）
   */
  handleSignalRequestByIds(data) {
    try {
      const { signalIds, source, callback } = data;
      console.log(`📨 [DEBUG] SignalManager.handleSignalRequestByIds() - 来源: ${source}, IDs: ${signalIds}`);

      // 根据ID获取信号
      const requestedSignals = signalIds
        .map(id => {
          const signal = this.signals.get(id);
          console.log(`🔍 [DEBUG] 查找信号 ID: ${id}, 找到: ${signal ? signal.name : '未找到'}`);
          return signal;
        })
        .filter(signal => signal !== undefined);

      console.log(`📤 [DEBUG] SignalManager: 返回 ${requestedSignals.length} 个指定信号，信号名称: ${requestedSignals.map(s => s.name)}`);

      // 调用回调函数返回信号
      if (callback && typeof callback === 'function') {
        console.log(`🔄 [DEBUG] SignalManager: 调用回调函数，传递 ${requestedSignals.length} 个信号`);
        callback(requestedSignals);
      } else {
        console.warn(`⚠️ [DEBUG] SignalManager: 没有回调函数或回调函数无效`);
      }

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '处理按ID查询信号请求';

    } catch (error) {
      console.error('❌ SignalManager: 处理按ID查询信号请求失败:', error);
      this.performance.errorCount++;

      if (data.callback && typeof data.callback === 'function') {
        data.callback([]);
      }
    }
  }

  /**
   * 处理发送信号请求（来自控制模块）
   */
  handleSendSignalRequest(data) {
    try {
      console.log(`📨 SignalManager: 收到发送信号请求 - 信号ID: ${data.signalId}`);

      // 发送信号
      this.sendSignal(data.signalId).then(success => {
        console.log(`📤 SignalManager: 信号发送结果: ${success}`);

        // 调用回调函数返回结果
        if (data.callback && typeof data.callback === 'function') {
          data.callback(success);
        }
      }).catch(error => {
        console.error('❌ SignalManager: 发送信号失败:', error);

        if (data.callback && typeof data.callback === 'function') {
          data.callback(false);
        }
      });

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '处理发送信号请求';

    } catch (error) {
      console.error('❌ SignalManager: 处理发送信号请求失败:', error);
      this.performance.errorCount++;

      if (data.callback && typeof data.callback === 'function') {
        data.callback(false);
      }
    }
  }

  /**
   * 处理批量发射响应（来自控制模块）
   */
  handleBatchEmitResponse(data) {
    try {
      console.log(`📨 SignalManager: 收到批量发射响应:`, data);

      if (data.success) {
        if (data.queued) {
          this.handleSuccess('批量发射已加入队列', '批量发射');
        } else {
          this.handleSuccess('批量发射已开始', '批量发射');
        }
      } else {
        this.handleError(new Error(data.error || '批量发射失败'), '批量发射');
      }

      // 更新性能统计
      this.performance.operationCount++;
      this.performance.lastOperation = '处理批量发射响应';

    } catch (error) {
      console.error('SignalManager: 处理批量发射响应失败:', error);
      this.performance.errorCount++;
    }
  }

  /**
   * 销毁模块 - 清理所有资源
   */
  destroy() {
    try {
      // 停止学习
      this.stopLearning();

      // 清理定时器
      if (this.autoSaveTimeout) {
        clearTimeout(this.autoSaveTimeout);
        this.autoSaveTimeout = null;
      }
      if (this.countdownInterval) {
        clearInterval(this.countdownInterval);
        this.countdownInterval = null;
      }

      // 清理事件监听器
      this.eventBus.off('signal.request.all');
      this.eventBus.off('signal.request.by-ids');
      this.eventBus.off('signal.send.request');
      this.eventBus.off('signal.edit.request');
      this.eventBus.off('signal.import.file.request');
      this.eventBus.off('signal.import.text.request');
      this.eventBus.off('signal.learn.form.submit');
      this.eventBus.off('signal.edit.form.submit');
      this.eventBus.off('signal.validation.request');

      // 清理数据
      this.signals.clear();
      this.selectedSignals.clear();

      console.log('SignalManager: 模块已销毁');
    } catch (error) {
      console.error('SignalManager: 销毁模块失败:', error);
    }
  }
}

// 导出到全局作用域 - 符合R1架构标准
if (typeof window !== 'undefined') {
  window.SignalManager = SignalManager;
  console.log('✅ SignalManager 类已导出到全局作用域 (仅支持标准格式)');
}
/**
 * @file SystemConfig.cpp
 * @brief 系统配置管理实现
 * 
 * 实现配置加载、保存、验证功能
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "SystemConfig.h"

// 全局实例
SystemConfig* g_systemConfig = nullptr;

SystemConfig::SystemConfig() 
    : configLoaded(false), configChanged(false) {
    g_systemConfig = this;
    setDefaults();
}

SystemConfig::~SystemConfig() {
    cleanup();
    g_systemConfig = nullptr;
}

bool SystemConfig::init() {
    Serial.println("[SystemConfig] 初始化系统配置...");
    
    if (!preferences.begin("system_config", false)) {
        Serial.println("[SystemConfig] Preferences初始化失败");
        return false;
    }
    
    Serial.println("[SystemConfig] 系统配置初始化完成");
    return true;
}

void SystemConfig::cleanup() {
    if (configChanged) {
        saveConfig();
    }
    preferences.end();
}

bool SystemConfig::saveConfig() {
    Serial.println("[SystemConfig] 保存配置...");
    
    bool success = true;
    success &= saveNetworkConfig();
    success &= saveHardwareConfig();
    success &= saveSystemConfig();
    success &= saveServerConfig();
    
    if (success) {
        configChanged = false;
        Serial.println("[SystemConfig] 配置保存成功");
    } else {
        Serial.println("[SystemConfig] 配置保存失败");
    }
    
    return success;
}

bool SystemConfig::loadConfig() {
    Serial.println("[SystemConfig] 加载配置...");
    
    bool success = true;
    success &= loadNetworkConfig();
    success &= loadHardwareConfig();
    success &= loadSystemConfig();
    success &= loadServerConfig();
    
    if (success) {
        configLoaded = true;
        configChanged = false;
        Serial.println("[SystemConfig] 配置加载成功");
    } else {
        Serial.println("[SystemConfig] 配置加载失败，使用默认配置");
        setDefaults();
    }
    
    return success;
}

bool SystemConfig::resetToDefaults() {
    Serial.println("[SystemConfig] 重置为默认配置...");
    
    setDefaults();
    configChanged = true;
    
    return saveConfig();
}

void SystemConfig::setDefaults() {
    // 网络配置默认值
    network = NetworkConfig();
    
    // 硬件配置默认值
    hardware = HardwareConfig();
    
    // 系统设置默认值
    system = SystemSettings();
    
    // 服务器配置默认值
    server = ServerConfig();
}

bool SystemConfig::saveNetworkConfig() {
    preferences.putString("wifi_ssid", network.wifiSSID.c_str());
    preferences.putString("wifi_pass", network.wifiPassword.c_str());
    preferences.putBool("wifi_enabled", network.wifiEnabled);
    preferences.putBool("static_ip", network.staticIP);
    preferences.putString("ip_addr", network.ipAddress.c_str());
    preferences.putString("gateway", network.gateway.c_str());
    preferences.putString("subnet", network.subnet.c_str());
    preferences.putString("dns1", network.dns1.c_str());
    preferences.putString("dns2", network.dns2.c_str());
    
    preferences.putString("ap_ssid", network.apSSID.c_str());
    preferences.putString("ap_pass", network.apPassword.c_str());
    preferences.putUChar("ap_channel", network.apChannel);
    preferences.putUChar("ap_max_conn", network.apMaxConnections);
    preferences.putBool("ap_hidden", network.apHidden);
    
    return true;
}

bool SystemConfig::loadNetworkConfig() {
    network.wifiSSID = preferences.getString("wifi_ssid", "").c_str();
    network.wifiPassword = preferences.getString("wifi_pass", "").c_str();
    network.wifiEnabled = preferences.getBool("wifi_enabled", false);
    network.staticIP = preferences.getBool("static_ip", false);
    network.ipAddress = preferences.getString("ip_addr", "*************").c_str();
    network.gateway = preferences.getString("gateway", "***********").c_str();
    network.subnet = preferences.getString("subnet", "*************").c_str();
    network.dns1 = preferences.getString("dns1", "*******").c_str();
    network.dns2 = preferences.getString("dns2", "*******").c_str();
    
    network.apSSID = preferences.getString("ap_ssid", "ESP32_IR_Controller").c_str();
    network.apPassword = preferences.getString("ap_pass", "12345678").c_str();
    network.apChannel = preferences.getUChar("ap_channel", 1);
    network.apMaxConnections = preferences.getUChar("ap_max_conn", 4);
    network.apHidden = preferences.getBool("ap_hidden", false);
    
    return true;
}

bool SystemConfig::saveHardwareConfig() {
    preferences.putUChar("ir_tx_pin", hardware.irTransmitPin);
    preferences.putUChar("ir_rx_pin", hardware.irReceivePin);
    preferences.putUChar("led_pin", hardware.statusLEDPin);
    preferences.putUChar("btn_pin", hardware.learnButtonPin);
    preferences.putUShort("ir_freq", hardware.irFrequency);
    preferences.putUChar("ir_duty", hardware.irDutyCycle);
    preferences.putBool("led_inv", hardware.ledInverted);
    preferences.putBool("btn_pullup", hardware.buttonPullup);
    
    return true;
}

bool SystemConfig::loadHardwareConfig() {
    hardware.irTransmitPin = preferences.getUChar("ir_tx_pin", 18);
    hardware.irReceivePin = preferences.getUChar("ir_rx_pin", 19);
    hardware.statusLEDPin = preferences.getUChar("led_pin", 2);
    hardware.learnButtonPin = preferences.getUChar("btn_pin", 0);
    hardware.irFrequency = preferences.getUShort("ir_freq", 38000);
    hardware.irDutyCycle = preferences.getUChar("ir_duty", 33);
    hardware.ledInverted = preferences.getBool("led_inv", false);
    hardware.buttonPullup = preferences.getBool("btn_pullup", true);
    
    return true;
}

bool SystemConfig::saveSystemConfig() {
    preferences.putString("dev_name", system.deviceName.c_str());
    preferences.putString("dev_desc", system.deviceDescription.c_str());
    preferences.putString("dev_loc", system.deviceLocation.c_str());
    preferences.putChar("timezone", system.timezone);
    preferences.putUChar("log_level", system.logLevel);
    preferences.putBool("auto_save", system.autoSave);
    preferences.putBool("led_enabled", system.ledEnabled);
    preferences.putUInt("learn_timeout", system.learningTimeout);
    preferences.putUInt("emit_timeout", system.emitTimeout);
    preferences.putUShort("max_signals", system.maxSignals);
    
    return true;
}

bool SystemConfig::loadSystemConfig() {
    system.deviceName = preferences.getString("dev_name", "红外控制器").c_str();
    system.deviceDescription = preferences.getString("dev_desc", "ESP32-S3红外控制系统").c_str();
    system.deviceLocation = preferences.getString("dev_loc", "客厅").c_str();
    system.timezone = preferences.getChar("timezone", 8);
    system.logLevel = preferences.getUChar("log_level", 3);
    system.autoSave = preferences.getBool("auto_save", true);
    system.ledEnabled = preferences.getBool("led_enabled", true);
    system.learningTimeout = preferences.getUInt("learn_timeout", 30000);
    system.emitTimeout = preferences.getUInt("emit_timeout", 5000);
    system.maxSignals = preferences.getUShort("max_signals", 1000);
    
    return true;
}

bool SystemConfig::saveServerConfig() {
    preferences.putUShort("http_port", server.httpPort);
    preferences.putUShort("ws_port", server.websocketPort);
    preferences.putUShort("max_conn", server.maxConnections);
    preferences.putUInt("req_timeout", server.requestTimeout);
    preferences.putBool("cors_enabled", server.corsEnabled);
    preferences.putString("cors_origin", server.corsOrigin.c_str());
    
    return true;
}

bool SystemConfig::loadServerConfig() {
    server.httpPort = preferences.getUShort("http_port", 80);
    server.websocketPort = preferences.getUShort("ws_port", 81);
    server.maxConnections = preferences.getUShort("max_conn", 10);
    server.requestTimeout = preferences.getUInt("req_timeout", 5000);
    server.corsEnabled = preferences.getBool("cors_enabled", true);
    server.corsOrigin = preferences.getString("cors_origin", "*").c_str();
    
    return true;
}

bool SystemConfig::updateNetwork(const NetworkConfig& config) {
    network = config;
    configChanged = true;
    return true;
}

bool SystemConfig::updateHardware(const HardwareConfig& config) {
    hardware = config;
    configChanged = true;
    return true;
}

bool SystemConfig::updateSystem(const SystemSettings& settings) {
    system = settings;
    configChanged = true;
    return true;
}

bool SystemConfig::updateServer(const ServerConfig& config) {
    server = config;
    configChanged = true;
    return true;
}

bool SystemConfig::validateConfig() const {
    // 基本验证
    if (network.apSSID.empty() || network.apPassword.length() < 8) {
        return false;
    }
    
    if (hardware.irFrequency < 30000 || hardware.irFrequency > 60000) {
        return false;
    }
    
    if (server.httpPort < 1024 || server.httpPort > 65535) {
        return false;
    }
    
    return true;
}

JsonDocument SystemConfig::toJson() const {
    JsonDocument doc;
    
    doc["network"] = networkToJson();
    doc["hardware"] = hardwareToJson();
    doc["system"] = systemToJson();
    doc["server"] = serverToJson();
    
    return doc;
}

JsonObject SystemConfig::networkToJson() const {
    JsonDocument doc;
    JsonObject obj = doc.to<JsonObject>();
    
    obj["wifi_ssid"] = network.wifiSSID;
    obj["wifi_enabled"] = network.wifiEnabled;
    obj["static_ip"] = network.staticIP;
    obj["ip_address"] = network.ipAddress;
    obj["gateway"] = network.gateway;
    obj["subnet"] = network.subnet;
    obj["dns1"] = network.dns1;
    obj["dns2"] = network.dns2;
    obj["ap_ssid"] = network.apSSID;
    obj["ap_channel"] = network.apChannel;
    obj["ap_max_connections"] = network.apMaxConnections;
    obj["ap_hidden"] = network.apHidden;
    
    return obj;
}

JsonObject SystemConfig::hardwareToJson() const {
    JsonDocument doc;
    JsonObject obj = doc.to<JsonObject>();
    
    obj["ir_transmit_pin"] = hardware.irTransmitPin;
    obj["ir_receive_pin"] = hardware.irReceivePin;
    obj["status_led_pin"] = hardware.statusLEDPin;
    obj["learn_button_pin"] = hardware.learnButtonPin;
    obj["ir_frequency"] = hardware.irFrequency;
    obj["ir_duty_cycle"] = hardware.irDutyCycle;
    obj["led_inverted"] = hardware.ledInverted;
    obj["button_pullup"] = hardware.buttonPullup;
    
    return obj;
}

JsonObject SystemConfig::systemToJson() const {
    JsonDocument doc;
    JsonObject obj = doc.to<JsonObject>();
    
    obj["device_name"] = system.deviceName;
    obj["device_description"] = system.deviceDescription;
    obj["device_location"] = system.deviceLocation;
    obj["timezone"] = system.timezone;
    obj["log_level"] = system.logLevel;
    obj["auto_save"] = system.autoSave;
    obj["led_enabled"] = system.ledEnabled;
    obj["learning_timeout"] = system.learningTimeout;
    obj["emit_timeout"] = system.emitTimeout;
    obj["max_signals"] = system.maxSignals;
    
    return obj;
}

JsonObject SystemConfig::serverToJson() const {
    JsonDocument doc;
    JsonObject obj = doc.to<JsonObject>();
    
    obj["http_port"] = server.httpPort;
    obj["websocket_port"] = server.websocketPort;
    obj["max_connections"] = server.maxConnections;
    obj["request_timeout"] = server.requestTimeout;
    obj["cors_enabled"] = server.corsEnabled;
    obj["cors_origin"] = server.corsOrigin;
    
    return obj;
}

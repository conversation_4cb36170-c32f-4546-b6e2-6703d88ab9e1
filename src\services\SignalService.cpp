/**
 * @file SignalService.cpp
 * @brief 信号管理服务实现
 * 
 * 实现信号CRUD操作的基础功能
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "SignalService.h"

SignalService::SignalService(EventManager* em) 
    : BaseService(em, "SignalService", ServiceType::CORE1_SERVICE) {
}

bool SignalService::init() {
    logInfo("初始化信号管理服务...");
    
    // 加载已保存的信号
    // TODO: 从存储加载信号
    
    setInitialized(true);
    logInfo("信号管理服务初始化完成");
    return true;
}

void SignalService::loop() {
    updateLoopStats();
    
    // 定期保存信号数据
    static uint32_t lastSave = 0;
    if (millis() - lastSave > 60000) { // 每分钟保存一次
        // TODO: 保存信号到存储
        lastSave = millis();
    }
}

void SignalService::cleanup() {
    logInfo("清理信号管理服务...");
    
    // 保存所有信号
    // TODO: 保存信号到存储
    
    signals.clear();
    logInfo("信号管理服务清理完成");
}

bool SignalService::addSignal(const SignalData& signal) {
    // 验证信号数据
    if (signal.id.empty() || signal.name.empty()) {
        logError("addSignal", "信号数据无效");
        return false;
    }
    
    // 检查是否已存在
    for (const auto& existing : signals) {
        if (existing.id == signal.id) {
            logWarning("addSignal", "信号ID已存在: " + signal.id);
            return false;
        }
    }
    
    // 添加信号
    signals.push_back(signal);
    
    logInfo("添加信号: " + signal.name + " (ID: " + signal.id + ")");
    
    // 发送事件
    JsonDocument eventData;
    eventData["signal_id"] = signal.id;
    eventData["signal_name"] = signal.name;
    emitEvent(EventType::SIGNAL_ADDED, eventData);
    
    return true;
}

SignalData SignalService::getSignal(const std::string& id) {
    for (const auto& signal : signals) {
        if (signal.id == id) {
            return signal;
        }
    }
    
    logWarning("getSignal", "信号未找到: " + id);
    return SignalData();
}

std::vector<SignalData> SignalService::getAllSignals() {
    return signals;
}

bool SignalService::updateSignal(const std::string& id, const SignalData& signal) {
    for (auto& existing : signals) {
        if (existing.id == id) {
            existing = signal;
            existing.id = id; // 确保ID不变
            
            logInfo("更新信号: " + signal.name + " (ID: " + id + ")");
            
            // 发送事件
            JsonDocument eventData;
            eventData["signal_id"] = id;
            eventData["signal_name"] = signal.name;
            emitEvent(EventType::SIGNAL_UPDATED, eventData);
            
            return true;
        }
    }
    
    logError("updateSignal", "信号未找到: " + id);
    return false;
}

bool SignalService::deleteSignal(const std::string& id) {
    for (auto it = signals.begin(); it != signals.end(); ++it) {
        if (it->id == id) {
            std::string name = it->name;
            signals.erase(it);
            
            logInfo("删除信号: " + name + " (ID: " + id + ")");
            
            // 发送事件
            JsonDocument eventData;
            eventData["signal_id"] = id;
            eventData["signal_name"] = name;
            emitEvent(EventType::SIGNAL_DELETED, eventData);
            
            return true;
        }
    }
    
    logError("deleteSignal", "信号未找到: " + id);
    return false;
}

bool SignalService::clearAllSignals() {
    size_t count = signals.size();
    signals.clear();
    
    logInfo("清空所有信号，共删除 " + std::to_string(count) + " 个信号");
    
    // 发送事件
    JsonDocument eventData;
    eventData["count"] = count;
    emitEvent(EventType::SIGNAL_DELETED, eventData);
    
    return true;
}

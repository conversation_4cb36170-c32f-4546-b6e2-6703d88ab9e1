/**
 * @file TaskData.h
 * @brief 任务数据结构定义 - 完全匹配前端格式
 * 
 * 基于前端完整数据文档的TaskData结构设计
 * 支持9个字段，完全匹配前端期望格式
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef TASK_DATA_H
#define TASK_DATA_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <string>
#include <vector>

enum class TaskStatus {
    PENDING,
    RUNNING,
    PAUSED,
    COMPLETED,
    FAILED
};

struct TaskData {
    // 核心字段 - 完全匹配前端TaskData格式
    std::string id;                     // task_12345678格式
    std::string name;                   // 任务名称
    std::string type;                   // 任务类型
    uint8_t priority;                   // 优先级1-4
    TaskStatus status;                  // 状态
    std::vector<std::string> signals;   // 信号ID数组
    JsonObject config;                  // 任务配置
    uint64_t created;                   // 创建时间
    uint64_t started;                   // 开始时间
    uint64_t completed;                 // 完成时间

    // 构造函数
    TaskData() : priority(2), status(TaskStatus::PENDING),
                 created(0), started(0), completed(0) {}

    // JSON序列化
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["id"] = id;
        doc["name"] = name;
        doc["type"] = type;
        doc["priority"] = priority;
        doc["status"] = statusToString(status);
        
        JsonArray signalsArray = doc["signals"].to<JsonArray>();
        for (const auto& signal : signals) {
            signalsArray.add(signal);
        }
        
        doc["config"] = config;
        doc["created"] = created;
        doc["started"] = started;
        doc["completed"] = completed;
        return doc;
    }

    // JSON反序列化
    static TaskData fromJson(const JsonObject& json) {
        TaskData task;
        task.id = json["id"].as<std::string>();
        task.name = json["name"].as<std::string>();
        task.type = json["type"].as<std::string>();
        task.priority = json["priority"].as<uint8_t>();
        task.status = statusFromString(json["status"].as<std::string>());
        
        JsonArray signalsArray = json["signals"];
        for (JsonVariant signal : signalsArray) {
            task.signals.push_back(signal.as<std::string>());
        }
        
        task.config = json["config"];
        task.created = json["created"].as<uint64_t>();
        task.started = json["started"].as<uint64_t>();
        task.completed = json["completed"].as<uint64_t>();
        return task;
    }

    // 状态转换
    static std::string statusToString(TaskStatus status) {
        switch (status) {
            case TaskStatus::PENDING: return "pending";
            case TaskStatus::RUNNING: return "running";
            case TaskStatus::PAUSED: return "paused";
            case TaskStatus::COMPLETED: return "completed";
            case TaskStatus::FAILED: return "failed";
            default: return "unknown";
        }
    }

    static TaskStatus statusFromString(const std::string& str) {
        if (str == "pending") return TaskStatus::PENDING;
        if (str == "running") return TaskStatus::RUNNING;
        if (str == "paused") return TaskStatus::PAUSED;
        if (str == "completed") return TaskStatus::COMPLETED;
        if (str == "failed") return TaskStatus::FAILED;
        return TaskStatus::PENDING;
    }

    // 辅助方法
    bool isValid() const {
        return !id.empty() && !name.empty() && priority >= 1 && priority <= 4;
    }

    void markStarted() {
        status = TaskStatus::RUNNING;
        started = millis();
    }

    void markCompleted(bool success = true) {
        status = success ? TaskStatus::COMPLETED : TaskStatus::FAILED;
        completed = millis();
    }

    void markPaused() {
        status = TaskStatus::PAUSED;
    }

    void markResumed() {
        status = TaskStatus::RUNNING;
    }

    // 获取执行时间
    uint64_t getExecutionTime() const {
        if (started == 0) return 0;
        uint64_t endTime = (completed > 0) ? completed : millis();
        return endTime - started;
    }

    // 检查是否超时
    bool isTimeout(uint64_t timeoutMs) const {
        if (status != TaskStatus::RUNNING || started == 0) return false;
        return (millis() - started) > timeoutMs;
    }

    // 生成JSON字符串
    std::string toJsonString() const {
        JsonDocument doc = toJson();
        std::string result;
        serializeJson(doc, result);
        return result;
    }

    // 从JSON字符串创建
    static TaskData fromJsonString(const std::string& jsonStr) {
        JsonDocument doc;
        deserializeJson(doc, jsonStr);
        return fromJson(doc.as<JsonObject>());
    }

    // 比较操作符
    bool operator==(const TaskData& other) const {
        return id == other.id;
    }

    bool operator!=(const TaskData& other) const {
        return !(*this == other);
    }

    // 用于优先级排序
    bool operator<(const TaskData& other) const {
        if (priority != other.priority) {
            return priority > other.priority; // 高优先级在前
        }
        return created < other.created; // 相同优先级按创建时间排序
    }
};

#endif

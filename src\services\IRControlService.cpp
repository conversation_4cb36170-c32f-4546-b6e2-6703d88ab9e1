/**
 * @file IRControlService.cpp
 * @brief 红外控制服务实现占位符
 */

#include "IRControlService.h"

IRControlService::IRControlService(EventManager* em) 
    : BaseService(em, "IRControlService", ServiceType::CORE1_SERVICE),
      learningState(LearningState::IDLE), emitState(EmitState::IDLE) {
}

IRControlService::~IRControlService() {
    cleanup();
}

bool IRControlService::init() {
    logInfo("初始化红外控制服务...");
    setInitialized(true);
    return true;
}

void IRControlService::loop() {
    updateLoopStats();
}

void IRControlService::cleanup() {
    logInfo("清理红外控制服务...");
}

bool IRControlService::startLearning(const LearningRequest& request) {
    logInfo("开始学习模式");
    learningState = LearningState::LEARNING;
    return true;
}

bool IRControlService::stopLearning() {
    logInfo("停止学习模式");
    learningState = LearningState::IDLE;
    return true;
}

bool IRControlService::emitSignal(const EmitRequest& request) {
    logInfo("发射信号: " + request.signalId);
    return true;
}

bool IRControlService::emitSignalById(const std::string& signalId, uint8_t repeat) {
    logInfo("发射信号: " + signalId);
    return true;
}

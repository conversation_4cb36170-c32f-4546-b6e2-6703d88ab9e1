/**
 * @file BatchRequestHandler.h
 * @brief 批量请求处理器 - 批量API请求处理
 * 
 * 基于前端完整数据文档的批量请求需求
 * 支持批量信号操作、事务处理
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef BATCH_REQUEST_HANDLER_H
#define BATCH_REQUEST_HANDLER_H

#include "../services/BaseService.h"
#include "../types/APITypes.h"
#include <vector>

class BatchRequestHandler : public BaseService {
private:
    uint32_t maxBatchSize;
    uint32_t batchTimeout;
    bool transactionMode;

public:
    BatchRequestHandler(EventManager* em);
    virtual ~BatchRequestHandler() = default;

    // BaseService接口实现
    bool init() override;
    void loop() override;
    void cleanup() override;

    // 批量处理接口
    std::vector<BatchResponse> processBatch(const std::vector<BatchRequest>& requests);
    BatchResponse processSingleRequest(const BatchRequest& request);

    // 配置
    void setMaxBatchSize(uint32_t size) { maxBatchSize = size; }
    void setBatchTimeout(uint32_t timeout) { batchTimeout = timeout; }
    void setTransactionMode(bool enabled) { transactionMode = enabled; }

private:
    bool validateBatchRequest(const std::vector<BatchRequest>& requests);
    void logBatchOperation(const std::vector<BatchRequest>& requests, 
                          const std::vector<BatchResponse>& responses);
};

#endif

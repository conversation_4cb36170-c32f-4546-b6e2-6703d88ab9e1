/**
 * ESP32-S3红外控制系统 - 全局配置头文件
 * 基于：ESP32-S3红外控制系统-双核并行后端架构设计文档.md 全局配置需求
 * 前端匹配：ESP32-S3红外控制系统-前端完整数据文档.md 系统配置需求
 * 
 * 前端需求分析：
 * - 系统配置: 全局系统配置和常量定义 (第3180行)
 * - 编译配置: 编译时配置和特性开关 (第3180行)
 * - 版本信息: 系统版本和构建信息 (第3180行)
 * - 调试配置: 调试级别和日志配置 (第1000行)
 * - 性能配置: 性能参数和优化配置 (第14行)
 * 
 * 后端架构要求：
 * - 全局配置: 系统全局配置定义 (第1-66行)
 * - 硬件配置: 硬件相关配置参数 (第1268-1320行)
 * - 网络配置: 网络相关配置参数 (第1634-1670行)
 * - 存储配置: 存储相关配置参数 (第229-260行)
 * - 性能配置: 性能优化配置参数 (第2754-2764行)
 * 
 * 功能：全局配置头文件，系统常量，编译配置，版本信息
 * 特性：全局配置、编译开关、版本管理、调试配置、性能参数
 */

#ifndef CONFIG_H
#define CONFIG_H

// ===== 系统版本信息 =====
#define SYSTEM_VERSION_MAJOR    1
#define SYSTEM_VERSION_MINOR    0
#define SYSTEM_VERSION_PATCH    0
#define SYSTEM_VERSION_BUILD    1
#define SYSTEM_VERSION_STRING   "1.0.0"
#define SYSTEM_BUILD_DATE       __DATE__
#define SYSTEM_BUILD_TIME       __TIME__
#define SYSTEM_NAME             "ESP32-S3红外控制系统"
#define SYSTEM_DESCRIPTION      "基于ESP32-S3的双核并行红外控制系统"

// ===== 硬件配置 =====
#define HARDWARE_ESP32_S3       1
#define HARDWARE_DUAL_CORE      1
#define HARDWARE_FLASH_SIZE     (8 * 1024 * 1024)  // 8MB Flash
#define HARDWARE_PSRAM_SIZE     (8 * 1024 * 1024)  // 8MB PSRAM
#define HARDWARE_CPU_FREQ       240                 // 240MHz CPU频率

// ===== 引脚配置 =====
#define PIN_IR_TRANSMIT         18                  // 红外发射引脚
#define PIN_IR_RECEIVE          19                  // 红外接收引脚
#define PIN_STATUS_LED          2                   // 状态LED引脚
#define PIN_BUTTON              0                   // 按钮引脚
#define PIN_I2C_SDA             21                  // I2C数据引脚
#define PIN_I2C_SCL             22                  // I2C时钟引脚

// ===== 网络配置 =====
#define NETWORK_HTTP_PORT       80                  // HTTP服务器端口
#define NETWORK_WEBSOCKET_PORT  81                  // WebSocket服务器端口
#define NETWORK_MAX_CONNECTIONS 5                   // 最大连接数
#define NETWORK_TIMEOUT         30000               // 30秒网络超时
#define NETWORK_RETRY_COUNT     3                   // 网络重试次数
#define NETWORK_RETRY_DELAY     5000                // 5秒重试延迟

// ===== WiFi配置 =====
#define WIFI_DEFAULT_SSID       "ESP32_IR_Controller"
#define WIFI_DEFAULT_PASSWORD   "12345678"
#define WIFI_CONNECT_TIMEOUT    30000               // 30秒连接超时
#define WIFI_RECONNECT_INTERVAL 60000               // 1分钟重连间隔
#define WIFI_MAX_RECONNECT      10                  // 最大重连次数

// ===== 红外配置 =====
#define IR_FREQUENCY            38000               // 38kHz载波频率
#define IR_DUTY_CYCLE           33                  // 33%占空比
#define IR_TIMEOUT              100000              // 100ms接收超时
#define IR_BUFFER_SIZE          512                 // 512字节缓冲区
#define IR_MAX_SIGNALS          100                 // 最大信号数量
#define IR_SIGNAL_TIMEOUT       30000               // 30秒信号超时

// ===== 存储配置 =====
#define STORAGE_NVS_NAMESPACE   "ir_controller"
#define STORAGE_SPIFFS_SIZE     (2 * 1024 * 1024)  // 2MB SPIFFS
#define STORAGE_CACHE_SIZE      50                  // 缓存大小
#define STORAGE_BACKUP_INTERVAL 3600000             // 1小时备份间隔
#define STORAGE_MAX_FILE_SIZE   (1 * 1024 * 1024)  // 1MB最大文件大小

// ===== 任务配置 =====
#define TASK_STACK_SIZE_SMALL   2048                // 小任务栈大小
#define TASK_STACK_SIZE_MEDIUM  4096                // 中等任务栈大小
#define TASK_STACK_SIZE_LARGE   8192                // 大任务栈大小
#define TASK_PRIORITY_LOW       1                   // 低优先级
#define TASK_PRIORITY_NORMAL    2                   // 普通优先级
#define TASK_PRIORITY_HIGH      3                   // 高优先级
#define TASK_PRIORITY_CRITICAL  4                   // 关键优先级

// ===== 定时器配置 =====
#define TIMER_TICK_INTERVAL     100                 // 100ms定时器间隔
#define TIMER_MAX_TASKS         50                  // 最大定时任务数
#define TIMER_PRECISION         1000                // 1ms精度
#define TIMER_WATCHDOG_TIMEOUT  60000               // 1分钟看门狗超时

// ===== 缓存配置 =====
#define CACHE_L1_SIZE           20                  // L1缓存大小
#define CACHE_MAX_MEMORY        (32 * 1024)        // 32KB最大缓存内存
#define CACHE_TTL_DEFAULT       300000              // 5分钟默认TTL
#define CACHE_CLEANUP_INTERVAL  60000               // 1分钟清理间隔
#define CACHE_HIT_RATE_TARGET   85                  // 85%目标命中率

// ===== 日志配置 =====
#define LOG_LEVEL_ERROR         0
#define LOG_LEVEL_WARNING       1
#define LOG_LEVEL_INFO          2
#define LOG_LEVEL_DEBUG         3
#define LOG_LEVEL_VERBOSE       4

#ifndef LOG_LEVEL
#define LOG_LEVEL               LOG_LEVEL_INFO      // 默认日志级别
#endif

#define LOG_BUFFER_SIZE         512                 // 日志缓冲区大小
#define LOG_MAX_MESSAGE_SIZE    256                 // 最大日志消息大小
#define LOG_TIMESTAMP_ENABLED   1                   // 启用时间戳
#define LOG_COLOR_ENABLED       1                   // 启用颜色输出

// ===== 安全配置 =====
#define SECURITY_ENABLED        1                   // 启用安全功能
#define SECURITY_MAX_LOGIN_ATTEMPTS 5               // 最大登录尝试次数
#define SECURITY_LOCKOUT_DURATION 300000            // 5分钟锁定时间
#define SECURITY_SESSION_TIMEOUT 3600000            // 1小时会话超时
#define SECURITY_MAX_SESSIONS   5                   // 最大并发会话数
#define SECURITY_ENCRYPTION_ENABLED 1               // 启用加密

// ===== 性能配置 =====
#define PERFORMANCE_MONITORING_ENABLED 1            // 启用性能监控
#define PERFORMANCE_UPDATE_INTERVAL 1000            // 1秒性能更新间隔
#define PERFORMANCE_HISTORY_SIZE 100                // 性能历史大小
#define PERFORMANCE_ALERT_THRESHOLD 90              // 90%性能警告阈值

// ===== 批处理配置 =====
#define BATCH_WINDOW_SIZE       50                  // 50ms批处理窗口
#define BATCH_MAX_SIZE          20                  // 最大批处理大小
#define BATCH_QUEUE_SIZE        100                 // 批处理队列大小
#define BATCH_TIMEOUT           5000                // 5秒批处理超时

// ===== WebSocket配置 =====
#define WEBSOCKET_HEARTBEAT_INTERVAL 30000          // 30秒心跳间隔
#define WEBSOCKET_MESSAGE_QUEUE_SIZE 32             // 消息队列大小
#define WEBSOCKET_MAX_MESSAGE_SIZE 1024             // 最大消息大小
#define WEBSOCKET_PING_TIMEOUT 10000                // 10秒ping超时

// ===== API配置 =====
#define API_REQUEST_TIMEOUT     5000                // 5秒API请求超时
#define API_MAX_REQUEST_SIZE    8192                // 8KB最大请求大小
#define API_RATE_LIMIT_WINDOW   60000               // 1分钟限流窗口
#define API_MAX_REQUESTS_PER_WINDOW 100             // 每分钟最大请求数
#define API_CORS_ENABLED        1                   // 启用CORS

// ===== OTA配置 =====
#define OTA_ENABLED             1                   // 启用OTA功能
#define OTA_TIMEOUT             300000              // 5分钟OTA超时
#define OTA_BUFFER_SIZE         4096                // 4KB OTA缓冲区
#define OTA_MAX_RETRY_COUNT     3                   // 最大重试次数
#define OTA_VERIFICATION_ENABLED 1                  // 启用OTA验证

// ===== 调试配置 =====
#ifdef DEBUG
#define DEBUG_ENABLED           1
#define DEBUG_SERIAL_SPEED      115200              // 串口调试速度
#define DEBUG_MEMORY_CHECK      1                   // 启用内存检查
#define DEBUG_TASK_MONITOR      1                   // 启用任务监控
#define DEBUG_PERFORMANCE_LOG   1                   // 启用性能日志
#else
#define DEBUG_ENABLED           0
#define DEBUG_MEMORY_CHECK      0
#define DEBUG_TASK_MONITOR      0
#define DEBUG_PERFORMANCE_LOG   0
#endif

// ===== 特性开关 =====
#define FEATURE_IR_LEARNING     1                   // 启用红外学习功能
#define FEATURE_TIMER_SERVICE   1                   // 启用定时器服务
#define FEATURE_DATA_EXPORT     1                   // 启用数据导出功能
#define FEATURE_BATCH_PROCESSING 1                  // 启用批处理功能
#define FEATURE_CACHE_OPTIMIZATION 1                // 启用缓存优化
#define FEATURE_PERFORMANCE_MONITORING 1            // 启用性能监控
#define FEATURE_SECURITY_MANAGER 1                  // 启用安全管理器
#define FEATURE_AUTO_BACKUP     1                   // 启用自动备份

// ===== 内存配置 =====
#define MEMORY_HEAP_SIZE        (320 * 1024)       // 320KB堆大小
#define MEMORY_STACK_SIZE       (8 * 1024)         // 8KB栈大小
#define MEMORY_DMA_SIZE         (32 * 1024)        // 32KB DMA大小
#define MEMORY_CACHE_SIZE       (64 * 1024)        // 64KB缓存大小
#define MEMORY_BUFFER_SIZE      (16 * 1024)        // 16KB缓冲区大小

// ===== 错误代码定义 =====
#define ERROR_SUCCESS           0
#define ERROR_INIT_FAILED       1001
#define ERROR_MEMORY_ALLOC      1002
#define ERROR_INVALID_PARAM     1003
#define ERROR_TIMEOUT           1004
#define ERROR_NOT_FOUND         1005
#define ERROR_PERMISSION_DENIED 1006
#define ERROR_NETWORK_FAILED    1007
#define ERROR_STORAGE_FAILED    1008
#define ERROR_HARDWARE_FAILED   1009
#define ERROR_UNKNOWN           9999

// ===== 状态代码定义 =====
#define STATUS_IDLE             0
#define STATUS_INITIALIZING     1
#define STATUS_RUNNING          2
#define STATUS_STOPPING         3
#define STATUS_ERROR            4
#define STATUS_MAINTENANCE      5

// ===== 编译时断言宏 =====
#define COMPILE_TIME_ASSERT(condition, message) \
    typedef char assertion_##message[(condition) ? 1 : -1]

// ===== 编译时检查 =====
COMPILE_TIME_ASSERT(HARDWARE_FLASH_SIZE >= 4 * 1024 * 1024, flash_size_too_small);
COMPILE_TIME_ASSERT(NETWORK_MAX_CONNECTIONS <= 10, too_many_connections);
COMPILE_TIME_ASSERT(CACHE_L1_SIZE <= 100, cache_size_too_large);
COMPILE_TIME_ASSERT(TIMER_MAX_TASKS <= 100, too_many_timer_tasks);

// ===== 版本兼容性检查 =====
#if ESP_IDF_VERSION < ESP_IDF_VERSION_VAL(4, 4, 0)
#error "需要ESP-IDF 4.4.0或更高版本"
#endif

// ===== 平台检查 =====
#ifndef CONFIG_IDF_TARGET_ESP32S3
#error "此代码仅支持ESP32-S3平台"
#endif

// ===== 功能依赖检查 =====
#if FEATURE_SECURITY_MANAGER && !SECURITY_ENABLED
#error "安全管理器需要启用安全功能"
#endif

#if FEATURE_CACHE_OPTIMIZATION && CACHE_L1_SIZE == 0
#error "缓存优化需要设置缓存大小"
#endif

#if FEATURE_PERFORMANCE_MONITORING && !PERFORMANCE_MONITORING_ENABLED
#error "性能监控功能需要启用性能监控"
#endif

// ===== 工具宏定义 =====
#define ARRAY_SIZE(arr)         (sizeof(arr) / sizeof((arr)[0]))
#define MIN(a, b)               ((a) < (b) ? (a) : (b))
#define MAX(a, b)               ((a) > (b) ? (a) : (b))
#define CLAMP(x, min, max)      (MIN(MAX(x, min), max))
#define UNUSED(x)               ((void)(x))

// ===== 字符串化宏 =====
#define STRINGIFY(x)            #x
#define TOSTRING(x)             STRINGIFY(x)

// ===== 位操作宏 =====
#define BIT(n)                  (1UL << (n))
#define SET_BIT(reg, bit)       ((reg) |= BIT(bit))
#define CLEAR_BIT(reg, bit)     ((reg) &= ~BIT(bit))
#define TOGGLE_BIT(reg, bit)    ((reg) ^= BIT(bit))
#define CHECK_BIT(reg, bit)     (((reg) & BIT(bit)) != 0)

// ===== 时间转换宏 =====
#define MS_TO_TICKS(ms)         ((ms) / portTICK_PERIOD_MS)
#define TICKS_TO_MS(ticks)      ((ticks) * portTICK_PERIOD_MS)
#define SECONDS_TO_MS(sec)      ((sec) * 1000)
#define MINUTES_TO_MS(min)      ((min) * 60 * 1000)
#define HOURS_TO_MS(hour)       ((hour) * 60 * 60 * 1000)

// ===== 内存对齐宏 =====
#define ALIGN_UP(size, align)   (((size) + (align) - 1) & ~((align) - 1))
#define ALIGN_DOWN(size, align) ((size) & ~((align) - 1))
#define IS_ALIGNED(ptr, align)  (((uintptr_t)(ptr) & ((align) - 1)) == 0)

// ===== 条件编译辅助宏 =====
#ifdef __cplusplus
#define EXTERN_C_BEGIN          extern "C" {
#define EXTERN_C_END            }
#else
#define EXTERN_C_BEGIN
#define EXTERN_C_END
#endif

#endif // CONFIG_H

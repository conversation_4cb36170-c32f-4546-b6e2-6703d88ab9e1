/**
 * @file EventManager.cpp
 * @brief 事件管理器实现 - 双核架构事件系统
 * 
 * 实现事件订阅、发布、队列处理和WebSocket事件支持
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include "EventManager.h"
#include "ErrorHandler.h"

// 全局实例
EventManager* g_eventManager = nullptr;

EventManager::EventManager() 
    : eventMutex(nullptr), queueMutex(nullptr), initialized(false),
      processingEvents(false), nextSubscriptionId(1),
      totalEventsProcessed(0), totalEventsQueued(0), maxQueueSize(0),
      lastProcessTime(0) {
    
    g_eventManager = this;
}

EventManager::~EventManager() {
    cleanup();
    g_eventManager = nullptr;
}

bool EventManager::init() {
    Serial.println("[EventManager] 初始化事件管理器...");
    
    // 创建互斥信号量
    eventMutex = xSemaphoreCreateMutex();
    if (eventMutex == nullptr) {
        Serial.println("[EventManager] 事件互斥量创建失败");
        return false;
    }
    
    queueMutex = xSemaphoreCreateMutex();
    if (queueMutex == nullptr) {
        Serial.println("[EventManager] 队列互斥量创建失败");
        return false;
    }
    
    // 重置统计信息
    resetStatistics();
    
    initialized = true;
    Serial.println("[EventManager] 事件管理器初始化完成");
    
    return true;
}

void EventManager::cleanup() {
    if (!initialized) return;
    
    Serial.println("[EventManager] 清理事件管理器...");
    
    initialized = false;
    processingEvents = false;
    
    // 清理所有订阅
    offAll();
    
    // 清理队列
    if (lockQueueMutex()) {
        while (!eventQueue.empty()) {
            eventQueue.pop();
        }
        unlockQueueMutex();
    }
    
    // 删除互斥量
    if (eventMutex) {
        vSemaphoreDelete(eventMutex);
        eventMutex = nullptr;
    }
    
    if (queueMutex) {
        vSemaphoreDelete(queueMutex);
        queueMutex = nullptr;
    }
    
    Serial.println("[EventManager] 事件管理器清理完成");
}

void EventManager::loop() {
    if (!initialized) return;
    
    uint32_t startTime = millis();
    
    // 处理事件队列
    processEventQueue();
    
    // 更新性能统计
    lastProcessTime = millis() - startTime;
    updateStatistics();
}

uint32_t EventManager::on(EventType type, EventHandler handler, EventPriority minPriority) {
    if (!initialized || !handler) return 0;
    
    EventSubscription subscription(handler, minPriority, false);
    return addSubscription(type, subscription);
}

uint32_t EventManager::once(EventType type, EventHandler handler, EventPriority minPriority) {
    if (!initialized || !handler) return 0;
    
    EventSubscription subscription(handler, minPriority, true);
    return addSubscription(type, subscription);
}

bool EventManager::off(EventType type, uint32_t subscriptionId) {
    if (!initialized) return false;
    
    if (subscriptionId == 0) {
        // 移除所有订阅
        offAll(type);
        return true;
    } else {
        // 移除特定订阅
        removeSubscription(type, subscriptionId);
        return true;
    }
}

void EventManager::offAll(EventType type) {
    if (!initialized) return;
    
    if (lockEventMutex()) {
        eventHandlers[type].clear();
        unlockEventMutex();
    }
}

void EventManager::offAll() {
    if (!initialized) return;
    
    if (lockEventMutex()) {
        eventHandlers.clear();
        unlockEventMutex();
    }
}

void EventManager::emit(EventType type, const JsonDocument& data) {
    if (!initialized) return;
    
    EventData event(type, data);
    event.generateId();
    event.setPriority(EventTypeUtils::getPriority(type));
    
    emit(event);
}

void EventManager::emit(const EventData& event) {
    if (!initialized) return;
    
    // 同步处理高优先级事件
    if (event.priority == EventPriority::CRITICAL) {
        processEvent(event);
    } else {
        // 异步处理其他事件
        emitAsync(event);
    }
}

void EventManager::emitAsync(EventType type, const JsonDocument& data) {
    if (!initialized) return;
    
    EventData event(type, data);
    event.generateId();
    event.setPriority(EventTypeUtils::getPriority(type));
    
    emitAsync(event);
}

void EventManager::emitAsync(const EventData& event) {
    if (!initialized) return;
    
    if (lockQueueMutex()) {
        eventQueue.push(event);
        totalEventsQueued++;
        
        // 更新最大队列大小
        if (eventQueue.size() > maxQueueSize) {
            maxQueueSize = eventQueue.size();
        }
        
        unlockQueueMutex();
    }
}

void EventManager::emitPriority(EventType type, EventPriority priority, const JsonDocument& data) {
    if (!initialized) return;
    
    EventData event(type, data);
    event.generateId();
    event.setPriority(priority);
    
    emit(event);
}

void EventManager::processEventQueue() {
    if (!initialized || processingEvents) return;
    
    processingEvents = true;
    
    // 处理队列中的事件
    while (true) {
        EventData event;
        bool hasEvent = false;
        
        // 从队列中取出事件
        if (lockQueueMutex()) {
            if (!eventQueue.empty()) {
                event = eventQueue.front();
                eventQueue.pop();
                hasEvent = true;
            }
            unlockQueueMutex();
        }
        
        if (!hasEvent) break;
        
        // 处理事件
        processEvent(event);
    }
    
    processingEvents = false;
}

void EventManager::processEvent(const EventData& event) {
    if (!initialized) return;
    
    try {
        // 检查事件过滤器
        if (eventFilter && !eventFilter(event)) {
            return;
        }
        
        // 检查事件是否应该处理
        if (!shouldProcessEvent(event)) {
            return;
        }
        
        // 执行事件处理器
        executeHandlers(event);
        
        // 事件路由
        if (eventRouter) {
            eventRouter(event);
        }
        
        totalEventsProcessed++;
        logEventProcessing(event, true);
        
    } catch (const std::exception& e) {
        handleEventError(event, e.what());
    } catch (...) {
        handleEventError(event, "未知错误");
    }
}

void EventManager::executeHandlers(const EventData& event) {
    if (!lockEventMutex()) return;
    
    auto it = eventHandlers.find(event.type);
    if (it != eventHandlers.end()) {
        auto& subscriptions = it->second;
        
        // 执行所有符合条件的处理器
        for (auto subIt = subscriptions.begin(); subIt != subscriptions.end();) {
            auto& subscription = *subIt;
            
            // 检查优先级
            if (event.priority >= subscription.minPriority) {
                try {
                    subscription.handler(event);
                } catch (const std::exception& e) {
                    handleEventError(event, "处理器异常: " + std::string(e.what()));
                } catch (...) {
                    handleEventError(event, "处理器未知异常");
                }
            }
            
            // 移除一次性订阅
            if (subscription.oneTime) {
                subIt = subscriptions.erase(subIt);
            } else {
                ++subIt;
            }
        }
    }
    
    unlockEventMutex();
}

uint32_t EventManager::addSubscription(EventType type, const EventSubscription& subscription) {
    if (!lockEventMutex()) return 0;
    
    EventSubscription sub = subscription;
    sub.subscriptionId = nextSubscriptionId++;
    
    eventHandlers[type].push_back(sub);
    
    unlockEventMutex();
    
    return sub.subscriptionId;
}

void EventManager::removeSubscription(EventType type, uint32_t subscriptionId) {
    if (!lockEventMutex()) return;
    
    auto it = eventHandlers.find(type);
    if (it != eventHandlers.end()) {
        auto& subscriptions = it->second;
        
        subscriptions.erase(
            std::remove_if(subscriptions.begin(), subscriptions.end(),
                [subscriptionId](const EventSubscription& sub) {
                    return sub.subscriptionId == subscriptionId;
                }),
            subscriptions.end()
        );
    }
    
    unlockEventMutex();
}

bool EventManager::lockEventMutex(uint32_t timeoutMs) {
    if (eventMutex == nullptr) return false;
    return xSemaphoreTake(eventMutex, pdMS_TO_TICKS(timeoutMs)) == pdTRUE;
}

void EventManager::unlockEventMutex() {
    if (eventMutex != nullptr) {
        xSemaphoreGive(eventMutex);
    }
}

bool EventManager::lockQueueMutex(uint32_t timeoutMs) {
    if (queueMutex == nullptr) return false;
    return xSemaphoreTake(queueMutex, pdMS_TO_TICKS(timeoutMs)) == pdTRUE;
}

void EventManager::unlockQueueMutex() {
    if (queueMutex != nullptr) {
        xSemaphoreGive(queueMutex);
    }
}

void EventManager::updateStatistics() {
    // 定期优化队列
    static uint32_t lastOptimize = 0;
    if (millis() - lastOptimize > 10000) { // 每10秒优化一次
        optimizeQueue();
        lastOptimize = millis();
    }
}

void EventManager::optimizeQueue() {
    if (!lockQueueMutex()) return;

    // 移除过期事件
    std::queue<EventData> newQueue;
    while (!eventQueue.empty()) {
        EventData event = eventQueue.front();
        eventQueue.pop();

        if (!event.isExpired()) {
            newQueue.push(event);
        }
    }

    eventQueue = std::move(newQueue);
    unlockQueueMutex();
}

bool EventManager::shouldProcessEvent(const EventData& event) {
    // 检查事件是否过期
    if (event.isExpired()) {
        return false;
    }

    // 检查目标核心
    if (event.targetCore != 255 && event.targetCore != xPortGetCoreID()) {
        return false;
    }

    return true;
}

void EventManager::handleEventError(const EventData& event, const std::string& error) {
    Serial.printf("[EventManager] 事件处理错误: %s, 事件类型: %s\n",
                  error.c_str(), EventTypeUtils::toString(event.type).c_str());

    logEventProcessing(event, false);
}

void EventManager::logEventProcessing(const EventData& event, bool success) {
    // 可以在这里添加详细的日志记录
    if (!success) {
        Serial.printf("[EventManager] 事件处理失败: %s\n",
                      EventTypeUtils::toString(event.type).c_str());
    }
}

JsonDocument EventManager::getStatistics() const {
    JsonDocument doc;
    doc["initialized"] = initialized;
    doc["processing"] = processingEvents;
    doc["queue_size"] = eventQueue.size();
    doc["max_queue_size"] = maxQueueSize;
    doc["total_processed"] = totalEventsProcessed;
    doc["total_queued"] = totalEventsQueued;
    doc["last_process_time"] = lastProcessTime;
    doc["subscription_count"] = eventHandlers.size();
    return doc;
}

void EventManager::resetStatistics() {
    totalEventsProcessed = 0;
    totalEventsQueued = 0;
    maxQueueSize = 0;
    lastProcessTime = 0;
}

void EventManager::setEventFilter(std::function<bool(const EventData&)> filter) {
    eventFilter = filter;
}

void EventManager::setEventRouter(std::function<void(const EventData&)> router) {
    eventRouter = router;
}

// EventUtils命名空间实现
namespace EventUtils {
    EventData createSystemEvent(EventType type, const std::string& message) {
        EventData event(type);
        event.generateId();
        event.data["message"] = message;
        event.data["timestamp"] = millis();
        return event;
    }

    EventData createNetworkEvent(EventType type, const std::string& status) {
        EventData event(type);
        event.generateId();
        event.data["status"] = status;
        event.data["timestamp"] = millis();
        return event;
    }

    EventData createSignalEvent(EventType type, const std::string& signalId,
                               const JsonDocument& signalData) {
        EventData event(type);
        event.generateId();
        event.data["signal_id"] = signalId;
        event.data["signal_data"] = signalData;
        event.data["timestamp"] = millis();
        return event;
    }

    EventData createErrorEvent(const std::string& error, const std::string& source) {
        EventData event(EventType::ERROR_OCCURRED);
        event.generateId();
        event.setPriority(EventPriority::HIGH);
        event.data["error"] = error;
        event.data["source"] = source;
        event.data["timestamp"] = millis();
        return event;
    }

    EventData createStatusEvent(const JsonDocument& statusData) {
        EventData event(EventType::STATUS_UPDATE);
        event.generateId();
        event.data["status"] = statusData;
        event.data["timestamp"] = millis();
        return event;
    }

    // WebSocket事件创建 (完全匹配前端需求)
    EventData createConnectedEvent(uint32_t clientId, const std::string& clientInfo) {
        EventData event(EventType::WEBSOCKET_CONNECTED);
        event.generateId();
        event.data["type"] = "connected";
        event.data["payload"]["message"] = "WebSocket连接成功";
        event.data["payload"]["clientId"] = "client_" + std::to_string(clientId);
        event.data["payload"]["serverTime"] = millis();
        event.data["payload"]["clientInfo"] = clientInfo;
        event.data["timestamp"] = millis();
        return event;
    }

    EventData createDisconnectedEvent(uint32_t clientId, const std::string& reason) {
        EventData event(EventType::WEBSOCKET_DISCONNECTED);
        event.generateId();
        event.data["type"] = "disconnected";
        event.data["payload"]["message"] = "WebSocket连接断开";
        event.data["payload"]["clientId"] = "client_" + std::to_string(clientId);
        event.data["payload"]["reason"] = reason;
        event.data["timestamp"] = millis();
        return event;
    }

    EventData createSignalLearnedEvent(const std::string& signalId,
                                      const JsonDocument& signalData,
                                      uint8_t quality) {
        EventData event(EventType::SIGNAL_LEARNED);
        event.generateId();
        event.data["type"] = "signal_learned";
        event.data["payload"]["signalId"] = signalId;
        event.data["payload"]["signalData"] = signalData;
        event.data["payload"]["quality"] = quality;
        event.data["payload"]["learningTime"] = millis();
        event.data["timestamp"] = millis();
        return event;
    }

    EventData createSignalSentEvent(const std::string& signalId,
                                   bool success,
                                   uint32_t duration) {
        EventData event(EventType::SIGNAL_SENT);
        event.generateId();
        event.data["type"] = "signal_sent";
        event.data["payload"]["signalId"] = signalId;
        event.data["payload"]["success"] = success;
        event.data["payload"]["duration"] = duration;
        event.data["timestamp"] = millis();
        return event;
    }
}

/**
 * 模式管理器 - 管理演示模式与生产模式的切换
 * 
 * 功能：
 * - 管理系统运行模式（演示模式/生产模式）
 * - 提供模式切换接口
 * - 管理不同模式的配置
 * - 处理模式切换时的状态保持
 */

class ModeManager {
  constructor() {
    console.log('🔄 初始化模式管理器...');
    
    // 当前模式：'demo' | 'production'
    this.currentMode = 'demo'; // 默认演示模式
    
    // 模式配置
    this.modeConfigs = {
      demo: {
        name: '演示模式',
        description: '使用模拟数据，无需硬件连接',
        icon: '🎭',
        esp32Enabled: false,
        simulationEnabled: true,
        showModeIndicator: true,
        baseURL: null,
        wsURL: null,
        features: {
          realHardware: false,
          realNetwork: false,
          dataSource: 'virtual',
          webSocketEvents: 'simulated'
        }
      },
      production: {
        name: '生产模式',
        description: '连接真实ESP32硬件',
        icon: '🔌',
        esp32Enabled: true,
        simulationEnabled: false,
        showModeIndicator: true,
        baseURL: 'http://127.0.0.1:8000',
        wsURL: 'ws://127.0.0.1:8001/ws',
        features: {
          realHardware: true,
          realNetwork: true,
          dataSource: 'esp32',
          webSocketEvents: 'real'
        }
      }
    };
    
    // 模式切换监听器
    this.modeChangeListeners = new Set();
    
    // 从本地存储恢复模式设置
    this.loadModeFromStorage();
    
    console.log(`✅ 模式管理器初始化完成，当前模式: ${this.getCurrentModeInfo().name}`);
  }

  /**
   * 获取当前模式
   */
  getCurrentMode() {
    return this.currentMode;
  }

  /**
   * 获取当前模式配置
   */
  getCurrentConfig() {
    return this.modeConfigs[this.currentMode];
  }

  /**
   * 获取当前模式信息
   */
  getCurrentModeInfo() {
    const config = this.getCurrentConfig();
    return {
      mode: this.currentMode,
      name: config.name,
      description: config.description,
      icon: config.icon,
      features: config.features
    };
  }

  /**
   * 切换模式
   */
  async switchMode(newMode) {
    if (!this.modeConfigs[newMode]) {
      throw new Error(`无效的模式: ${newMode}`);
    }

    if (newMode === this.currentMode) {
      console.log(`已经是${this.modeConfigs[newMode].name}，无需切换`);
      return false;
    }

    const oldMode = this.currentMode;
    const oldConfig = this.getCurrentConfig();
    const newConfig = this.modeConfigs[newMode];

    console.log(`🔄 开始切换模式: ${oldConfig.name} → ${newConfig.name}`);

    try {
      // 1. 执行切换前的清理工作
      await this.beforeModeSwitch(oldMode, newMode);

      // 2. 更新当前模式
      this.currentMode = newMode;

      // 3. 保存到本地存储
      this.saveModeToStorage();

      // 4. 执行切换后的初始化工作
      await this.afterModeSwitch(oldMode, newMode);

      // 5. 通知所有监听器
      this.notifyModeChange(oldMode, newMode);

      console.log(`✅ 模式切换完成: ${newConfig.name}`);
      return true;

    } catch (error) {
      console.error('❌ 模式切换失败:', error);
      // 回滚到原模式
      this.currentMode = oldMode;
      throw error;
    }
  }

  /**
   * 模式切换前的清理工作
   */
  async beforeModeSwitch(oldMode, newMode) {
    console.log(`🧹 执行模式切换前清理: ${oldMode} → ${newMode}`);

    if (oldMode === 'production') {
      // 从生产模式切换出来时，断开真实连接
      console.log('🔌 断开真实ESP32连接...');
      // 这里会由ESP32Communicator处理具体的断开逻辑
    }

    if (oldMode === 'demo') {
      // 从演示模式切换出来时，停止虚拟后端
      console.log('🎭 停止虚拟后端系统...');
      // 这里会由VirtualBackendSystem处理具体的停止逻辑
    }
  }

  /**
   * 模式切换后的初始化工作
   */
  async afterModeSwitch(oldMode, newMode) {
    console.log(`🚀 执行模式切换后初始化: ${oldMode} → ${newMode}`);

    if (newMode === 'production') {
      // 切换到生产模式时，准备真实连接
      console.log('🔌 准备连接真实ESP32...');
      // 这里会由ESP32Communicator处理具体的连接逻辑
    }

    if (newMode === 'demo') {
      // 切换到演示模式时，启动虚拟后端
      console.log('🎭 启动虚拟后端系统...');
      // 这里会由VirtualBackendSystem处理具体的启动逻辑
    }
  }

  /**
   * 添加模式切换监听器
   */
  addModeChangeListener(listener) {
    this.modeChangeListeners.add(listener);
    console.log(`📡 添加模式切换监听器，当前监听器数量: ${this.modeChangeListeners.size}`);
  }

  /**
   * 移除模式切换监听器
   */
  removeModeChangeListener(listener) {
    this.modeChangeListeners.delete(listener);
    console.log(`📡 移除模式切换监听器，当前监听器数量: ${this.modeChangeListeners.size}`);
  }

  /**
   * 通知模式切换
   */
  notifyModeChange(oldMode, newMode) {
    const changeEvent = {
      oldMode: oldMode,
      newMode: newMode,
      oldConfig: this.modeConfigs[oldMode],
      newConfig: this.modeConfigs[newMode],
      timestamp: Date.now()
    };

    console.log(`📢 通知模式切换事件，监听器数量: ${this.modeChangeListeners.size}`);

    this.modeChangeListeners.forEach(listener => {
      try {
        if (typeof listener === 'function') {
          listener(changeEvent);
        } else if (listener.onModeChange) {
          listener.onModeChange(changeEvent);
        }
      } catch (error) {
        console.error('模式切换监听器执行失败:', error);
      }
    });
  }

  /**
   * 检查模式兼容性
   */
  checkModeCompatibility(mode) {
    if (mode === 'production') {
      // 检查生产模式的前置条件
      return {
        compatible: true,
        warnings: [
          '需要ESP32-S3硬件正常连接',
          '需要后端服务器运行在127.0.0.1:8000',
          '需要WebSocket服务运行在127.0.0.1:8001'
        ]
      };
    }

    return {
      compatible: true,
      warnings: []
    };
  }

  /**
   * 获取模式切换确认信息
   */
  getModeConfirmationInfo(targetMode) {
    const currentConfig = this.getCurrentConfig();
    const targetConfig = this.modeConfigs[targetMode];
    const compatibility = this.checkModeCompatibility(targetMode);

    return {
      current: {
        mode: this.currentMode,
        name: currentConfig.name,
        description: currentConfig.description
      },
      target: {
        mode: targetMode,
        name: targetConfig.name,
        description: targetConfig.description
      },
      compatibility: compatibility,
      confirmMessage: this.generateConfirmMessage(targetMode, compatibility)
    };
  }

  /**
   * 生成确认消息
   */
  generateConfirmMessage(targetMode, compatibility) {
    const targetConfig = this.modeConfigs[targetMode];
    const currentConfig = this.getCurrentConfig();

    let message = `确定要切换到${targetConfig.name}吗？\n\n`;
    message += `当前状态：${currentConfig.name}\n`;
    message += `- ${currentConfig.description}\n\n`;
    message += `切换后状态：${targetConfig.name}\n`;
    message += `- ${targetConfig.description}\n`;

    if (compatibility.warnings.length > 0) {
      message += `\n⚠️ 注意事项：\n`;
      compatibility.warnings.forEach(warning => {
        message += `- ${warning}\n`;
      });
    }

    return message;
  }

  /**
   * 保存模式到本地存储
   */
  saveModeToStorage() {
    try {
      localStorage.setItem('r1_system_mode', this.currentMode);
      console.log(`💾 模式已保存到本地存储: ${this.currentMode}`);
    } catch (error) {
      console.warn('保存模式到本地存储失败:', error);
    }
  }

  /**
   * 从本地存储加载模式
   */
  loadModeFromStorage() {
    try {
      const savedMode = localStorage.getItem('r1_system_mode');
      if (savedMode && this.modeConfigs[savedMode]) {
        this.currentMode = savedMode;
        console.log(`📂 从本地存储恢复模式: ${savedMode}`);
      }
    } catch (error) {
      console.warn('从本地存储加载模式失败:', error);
    }
  }

  /**
   * 重置为默认模式
   */
  resetToDefaultMode() {
    console.log('🔄 重置为默认模式...');
    this.currentMode = 'demo';
    this.saveModeToStorage();
    console.log('✅ 已重置为演示模式');
  }

  /**
   * 获取所有可用模式
   */
  getAllModes() {
    return Object.keys(this.modeConfigs).map(mode => ({
      mode: mode,
      ...this.modeConfigs[mode]
    }));
  }
}

// 导出模式管理器类
window.ModeManager = ModeManager;

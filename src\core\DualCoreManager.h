/**
 * @file DualCoreManager.h
 * @brief 双核管理器 - ESP32-S3双核并行架构核心
 * 
 * 基于前端完整数据文档的功能需求设计
 * Core 0: 网络通信、API处理、WebSocket
 * Core 1: 红外硬件控制、信号学习发射
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef DUAL_CORE_MANAGER_H
#define DUAL_CORE_MANAGER_H

#include <Arduino.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <freertos/semphr.h>
#include <memory>
#include <vector>
#include "../types/EventTypes.h"
#include "EventManager.h"
#include "ErrorHandler.h"

// 双核任务优先级定义
#define CORE0_NETWORK_PRIORITY      2
#define CORE0_API_PRIORITY          3
#define CORE0_WEBSOCKET_PRIORITY    2
#define CORE1_IR_CONTROL_PRIORITY   4
#define CORE1_LEARNING_PRIORITY     5
#define CORE1_EMIT_PRIORITY         4

// 双核间通信队列大小
#define INTER_CORE_QUEUE_SIZE       32
#define EVENT_QUEUE_SIZE            64

// 前向声明
class NetworkManager;
class HardwareManager;
class BaseService;

class DualCoreManager {
private:
    // 双核任务句柄
    TaskHandle_t core0TaskHandle;
    TaskHandle_t core1TaskHandle;
    
    // 双核间通信
    QueueHandle_t core0ToCore1Queue;
    QueueHandle_t core1ToCore0Queue;
    SemaphoreHandle_t interCoreMutex;
    
    // 核心组件
    std::unique_ptr<EventManager> eventManager;
    std::unique_ptr<ErrorHandler> errorHandler;
    std::unique_ptr<NetworkManager> networkManager;
    std::unique_ptr<HardwareManager> hardwareManager;
    
    // 服务管理
    std::vector<std::unique_ptr<BaseService>> core0Services;
    std::vector<std::unique_ptr<BaseService>> core1Services;
    
    // 系统状态
    bool systemInitialized;
    bool core0Ready;
    bool core1Ready;
    uint32_t systemStartTime;
    uint32_t lastHeartbeat;
    
    // 性能监控
    uint32_t core0TaskCount;
    uint32_t core1TaskCount;
    uint32_t interCoreMessageCount;

public:
    DualCoreManager();
    ~DualCoreManager();

    // 系统生命周期
    bool init();
    void start();
    void stop();
    void restart();

    // 双核任务管理
    bool startCore0Tasks();
    bool startCore1Tasks();
    void stopAllTasks();

    // 双核间通信
    bool sendToCore0(const EventData& event);
    bool sendToCore1(const EventData& event);
    bool broadcastEvent(const EventData& event);

    // 服务管理
    template<typename T, typename... Args>
    T* addCore0Service(Args&&... args) {
        auto service = std::make_unique<T>(eventManager.get(), std::forward<Args>(args)...);
        T* servicePtr = service.get();
        core0Services.push_back(std::move(service));
        return servicePtr;
    }

    template<typename T, typename... Args>
    T* addCore1Service(Args&&... args) {
        auto service = std::make_unique<T>(eventManager.get(), std::forward<Args>(args)...);
        T* servicePtr = service.get();
        core1Services.push_back(std::move(service));
        return servicePtr;
    }

    // 状态查询
    bool isInitialized() const { return systemInitialized; }
    bool isCore0Ready() const { return core0Ready; }
    bool isCore1Ready() const { return core1Ready; }
    uint32_t getUptime() const { return millis() - systemStartTime; }
    
    // 性能统计
    uint32_t getCore0TaskCount() const { return core0TaskCount; }
    uint32_t getCore1TaskCount() const { return core1TaskCount; }
    uint32_t getInterCoreMessageCount() const { return interCoreMessageCount; }
    
    // 获取管理器
    EventManager* getEventManager() { return eventManager.get(); }
    ErrorHandler* getErrorHandler() { return errorHandler.get(); }
    NetworkManager* getNetworkManager() { return networkManager.get(); }
    HardwareManager* getHardwareManager() { return hardwareManager.get(); }

private:
    // 双核任务函数
    static void core0TaskFunction(void* parameter);
    static void core1TaskFunction(void* parameter);
    
    // 核心处理循环
    void core0Loop();
    void core1Loop();
    
    // 双核间消息处理
    void processCore0Messages();
    void processCore1Messages();
    void handleInterCoreEvent(const EventData& event);
    
    // 初始化函数
    bool initializeQueues();
    bool initializeCore0();
    bool initializeCore1();
    bool initializeServices();
    
    // 心跳和监控
    void sendHeartbeat();
    void checkCoreHealth();
    void updatePerformanceStats();
    
    // 错误处理
    void handleCoreError(uint8_t coreId, const std::string& error);
    void handleTaskFailure(TaskHandle_t taskHandle, const std::string& taskName);
    
    // 资源清理
    void cleanupCore0();
    void cleanupCore1();
    void cleanupQueues();
};

// 全局实例访问
extern DualCoreManager* g_dualCoreManager;

// 便捷宏定义
#define SEND_TO_CORE0(event) g_dualCoreManager->sendToCore0(event)
#define SEND_TO_CORE1(event) g_dualCoreManager->sendToCore1(event)
#define BROADCAST_EVENT(event) g_dualCoreManager->broadcastEvent(event)

// 核心检查宏
#define IS_CORE0() (xPortGetCoreID() == 0)
#define IS_CORE1() (xPortGetCoreID() == 1)
#define CURRENT_CORE() xPortGetCoreID()

// 任务创建辅助宏
#define CREATE_CORE0_TASK(func, name, stack, param, priority, handle) \
    xTaskCreatePinnedToCore(func, name, stack, param, priority, handle, 0)

#define CREATE_CORE1_TASK(func, name, stack, param, priority, handle) \
    xTaskCreatePinnedToCore(func, name, stack, param, priority, handle, 1)

#endif

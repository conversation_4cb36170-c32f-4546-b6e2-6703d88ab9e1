/**
 * @file main.cpp
 * @brief ESP32-S3红外控制系统主程序 - 双核并行架构
 * 
 * 基于前端完整数据文档的功能需求实现
 * Core 0: 网络通信、HTTP API、WebSocket
 * Core 1: 红外硬件控制、信号学习发射
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#include <Arduino.h>
#include <WiFi.h>
#include <ESPAsyncWebServer.h>
#include <AsyncTCP.h>
#include <ArduinoJson.h>
#include <Preferences.h>
#include <SPIFFS.h>

// 核心组件
#include "core/DualCoreManager.h"
#include "core/EventManager.h"
#include "core/ErrorHandler.h"

// 网络组件 (Core 0)
#include "network/WiFiManager.h"
#include "network/WebServerManager.h"
#include "network/WebSocketManager.h"
#include "network/APIRouter.h"

// 硬件组件 (Core 1)
#include "hardware/IRTransmitter.h"
#include "hardware/IRReceiver.h"
#include "hardware/StatusLED.h"
#include "hardware/HardwareManager.h"

// 服务组件
#include "services/SignalService.h"
#include "services/IRControlService.h"
#include "services/StatusService.h"
#include "services/ConfigService.h"
#include "services/TimerService.h"
#include "services/OTAService.h"

// 存储组件
#include "storage/SignalStorage.h"
#include "storage/FlashStorage.h"

// 配置文件
#include "config/SystemConfig.h"
#include "config/PinConfig.h"
#include "config/NetworkConfig.h"

// 数据类型
#include "types/SignalData.h"
#include "types/TaskData.h"
#include "types/APITypes.h"
#include "types/EventTypes.h"

// 全局组件实例
std::unique_ptr<DualCoreManager> g_dualCoreManager;
std::unique_ptr<SystemConfig> g_systemConfig;

// 系统状态
bool g_systemInitialized = false;
uint32_t g_systemStartTime = 0;

// 前向声明
void initializeSystem();
void setupCore0Services();
void setupCore1Services();
void handleSystemError(const std::string& error);
void printSystemInfo();

void setup() {
    // 初始化串口
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("========================================");
    Serial.println("ESP32-S3红外控制系统 v2.0");
    Serial.println("双核并行架构启动中...");
    Serial.println("========================================");
    
    g_systemStartTime = millis();
    
    // 打印系统信息
    printSystemInfo();
    
    // 初始化系统
    initializeSystem();
    
    Serial.println("========================================");
    Serial.println("系统启动完成！");
    Serial.printf("启动耗时: %lu ms\n", millis() - g_systemStartTime);
    Serial.println("========================================");
}

void loop() {
    // 主循环由双核管理器接管
    // 这里只做基本的看门狗和错误监控
    
    static uint32_t lastHeartbeat = 0;
    static uint32_t heartbeatInterval = 5000; // 5秒心跳
    
    if (millis() - lastHeartbeat > heartbeatInterval) {
        // 检查系统健康状态
        if (g_dualCoreManager && g_dualCoreManager->isInitialized()) {
            if (!g_dualCoreManager->isCore0Ready() || !g_dualCoreManager->isCore1Ready()) {
                Serial.println("[Main] 检测到核心异常，尝试重启...");
                g_dualCoreManager->restart();
            }
        }
        
        lastHeartbeat = millis();
    }
    
    // 短暂延迟，避免占用过多CPU
    delay(100);
}

void initializeSystem() {
    Serial.println("[Main] 开始系统初始化...");
    
    // 1. 初始化文件系统
    Serial.println("[Main] 初始化SPIFFS文件系统...");
    if (!SPIFFS.begin(true)) {
        Serial.println("[Main] SPIFFS初始化失败！");
        handleSystemError("SPIFFS初始化失败");
        return;
    }
    Serial.println("[Main] SPIFFS初始化成功");
    
    // 2. 加载系统配置
    Serial.println("[Main] 加载系统配置...");
    g_systemConfig = std::make_unique<SystemConfig>();
    if (!g_systemConfig->init()) {
        Serial.println("[Main] 系统配置初始化失败！");
        handleSystemError("系统配置初始化失败");
        return;
    }
    
    if (!g_systemConfig->loadConfig()) {
        Serial.println("[Main] 配置文件加载失败，使用默认配置");
        g_systemConfig->resetToDefaults();
        g_systemConfig->saveConfig();
    }
    Serial.println("[Main] 系统配置加载完成");
    
    // 3. 初始化双核管理器
    Serial.println("[Main] 初始化双核管理器...");
    g_dualCoreManager = std::make_unique<DualCoreManager>();
    if (!g_dualCoreManager->init()) {
        Serial.println("[Main] 双核管理器初始化失败！");
        handleSystemError("双核管理器初始化失败");
        return;
    }
    Serial.println("[Main] 双核管理器初始化成功");
    
    // 4. 设置Core 0服务 (网络通信)
    Serial.println("[Main] 设置Core 0服务...");
    setupCore0Services();
    
    // 5. 设置Core 1服务 (硬件控制)
    Serial.println("[Main] 设置Core 1服务...");
    setupCore1Services();
    
    // 6. 启动双核任务
    Serial.println("[Main] 启动双核任务...");
    g_dualCoreManager->start();
    
    // 7. 等待系统就绪
    Serial.println("[Main] 等待系统就绪...");
    uint32_t timeout = millis() + 10000; // 10秒超时
    while (!g_dualCoreManager->isCore0Ready() || !g_dualCoreManager->isCore1Ready()) {
        if (millis() > timeout) {
            Serial.println("[Main] 系统启动超时！");
            handleSystemError("系统启动超时");
            return;
        }
        delay(100);
    }
    
    g_systemInitialized = true;
    Serial.println("[Main] 系统初始化完成");
}

void setupCore0Services() {
    // Core 0: 网络通信、API处理、WebSocket
    
    // WiFi管理服务
    auto wifiManager = g_dualCoreManager->addCore0Service<WiFiManager>();
    
    // Web服务器管理
    auto webServerManager = g_dualCoreManager->addCore0Service<WebServerManager>();
    
    // WebSocket管理
    auto webSocketManager = g_dualCoreManager->addCore0Service<WebSocketManager>();
    
    // API路由器
    auto apiRouter = g_dualCoreManager->addCore0Service<APIRouter>();
    
    // 状态服务
    auto statusService = g_dualCoreManager->addCore0Service<StatusService>();
    
    // 配置服务
    auto configService = g_dualCoreManager->addCore0Service<ConfigService>();
    
    // OTA升级服务
    auto otaService = g_dualCoreManager->addCore0Service<OTAService>();
    
    Serial.println("[Main] Core 0服务设置完成");
}

void setupCore1Services() {
    // Core 1: 硬件控制、红外处理
    
    // 信号管理服务
    auto signalService = g_dualCoreManager->addCore1Service<SignalService>();
    
    // 红外控制服务
    auto irControlService = g_dualCoreManager->addCore1Service<IRControlService>();
    
    // 定时器服务
    auto timerService = g_dualCoreManager->addCore1Service<TimerService>();
    
    Serial.println("[Main] Core 1服务设置完成");
}

void handleSystemError(const std::string& error) {
    Serial.printf("[Main] 系统错误: %s\n", error.c_str());
    
    // 尝试重启系统
    Serial.println("[Main] 尝试重启系统...");
    delay(1000);
    ESP.restart();
}

void printSystemInfo() {
    Serial.println("[Main] 系统信息:");
    Serial.printf("  芯片型号: %s\n", ESP.getChipModel());
    Serial.printf("  芯片版本: %d\n", ESP.getChipRevision());
    Serial.printf("  CPU频率: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("  Flash大小: %d MB\n", ESP.getFlashChipSize() / (1024 * 1024));
    Serial.printf("  可用堆内存: %d bytes\n", ESP.getFreeHeap());
    Serial.printf("  最大分配内存: %d bytes\n", ESP.getMaxAllocHeap());
    Serial.printf("  PSRAM大小: %d bytes\n", ESP.getPsramSize());
    Serial.printf("  可用PSRAM: %d bytes\n", ESP.getFreePsram());
    Serial.printf("  SDK版本: %s\n", ESP.getSdkVersion());
    Serial.printf("  编译时间: %s %s\n", __DATE__, __TIME__);
    Serial.println();
}

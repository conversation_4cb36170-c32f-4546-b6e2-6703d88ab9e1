/**
 * @file SignalStorage.h
 * @brief 信号存储管理器 - 信号数据持久化
 * 
 * 基于前端完整数据文档的信号存储需求
 * 支持CRUD操作、缓存管理、Flash持久化
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef SIGNAL_STORAGE_H
#define SIGNAL_STORAGE_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include <unordered_map>
#include <string>
#include "../types/SignalData.h"

// 前向声明
class FlashStorage;
class CacheManager;

// 存储统计信息
struct StorageStats {
    uint32_t totalSignals;
    uint32_t cacheHits;
    uint32_t cacheMisses;
    uint32_t flashReads;
    uint32_t flashWrites;
    uint32_t lastSaveTime;
    size_t memoryUsage;
    
    StorageStats() : totalSignals(0), cacheHits(0), cacheMisses(0),
                     flashReads(0), flashWrites(0), lastSaveTime(0), memoryUsage(0) {}
};

class SignalStorage {
private:
    std::unique_ptr<FlashStorage> flashStorage;
    std::unique_ptr<CacheManager> cacheManager;
    
    // 内存缓存
    std::unordered_map<std::string, SignalData> signalCache;
    std::vector<std::string> signalIndex;
    bool cacheLoaded;
    bool cacheModified;
    
    // 配置
    size_t maxCacheSize;
    uint32_t autoSaveInterval;
    uint32_t lastSaveTime;
    
    // 统计信息
    StorageStats stats;

public:
    SignalStorage();
    ~SignalStorage();

    // 生命周期
    bool init();
    void cleanup();
    void loop();

    // 信号CRUD操作 (匹配前端API需求)
    bool addSignal(const SignalData& signal);
    SignalData getSignal(const std::string& id);
    std::vector<SignalData> getAllSignals();
    std::vector<SignalData> getSignalsByType(const std::string& type);
    bool updateSignal(const std::string& id, const SignalData& signal);
    bool deleteSignal(const std::string& id);
    bool clearAllSignals();
    
    // 批量操作
    bool addSignals(const std::vector<SignalData>& signals);
    bool deleteSignals(const std::vector<std::string>& ids);
    std::vector<SignalData> getSignals(const std::vector<std::string>& ids);
    
    // 查询操作
    bool signalExists(const std::string& id);
    size_t getSignalCount();
    size_t getSignalCountByType(const std::string& type);
    std::vector<std::string> getSignalIds();
    std::vector<std::string> getSignalTypes();
    
    // 搜索功能
    std::vector<SignalData> searchSignals(const std::string& query);
    std::vector<SignalData> searchByName(const std::string& name);
    std::vector<SignalData> searchByDescription(const std::string& description);
    
    // 持久化控制
    bool saveToFlash();
    bool loadFromFlash();
    bool exportSignals(const std::string& filename);
    bool importSignals(const std::string& filename);
    
    // 缓存管理
    void clearCache();
    void refreshCache();
    bool isCacheLoaded() const { return cacheLoaded; }
    size_t getCacheSize() const { return signalCache.size(); }
    
    // 统计信息
    const StorageStats& getStats() const { return stats; }
    JsonDocument getStatsJson() const;
    void resetStats();
    
    // 配置管理
    void setMaxCacheSize(size_t size) { maxCacheSize = size; }
    void setAutoSaveInterval(uint32_t interval) { autoSaveInterval = interval; }
    
    // 数据验证
    bool validateSignal(const SignalData& signal);
    bool validateSignalId(const std::string& id);
    std::vector<std::string> getValidationErrors(const SignalData& signal);

private:
    // 缓存操作
    void loadSignalToCache(const SignalData& signal);
    void removeSignalFromCache(const std::string& id);
    void updateCacheIndex();
    void optimizeCache();
    
    // Flash操作
    bool saveSignalToFlash(const SignalData& signal);
    SignalData loadSignalFromFlash(const std::string& id);
    bool deleteSignalFromFlash(const std::string& id);
    
    // 内部辅助
    std::string generateSignalId();
    bool isValidSignalId(const std::string& id);
    void updateStats(const std::string& operation);
    void markCacheModified();
    
    // 自动保存
    void checkAutoSave();
    bool needsAutoSave();
    
    // 内存管理
    void checkMemoryUsage();
    void freeMemoryIfNeeded();
    size_t calculateMemoryUsage();
    
    // 数据转换
    JsonDocument signalToJson(const SignalData& signal);
    SignalData signalFromJson(const JsonObject& json);
    
    // 错误处理
    void handleStorageError(const std::string& operation, const std::string& error);
    void logStorageOperation(const std::string& operation, bool success);
};

// 全局存储实例
extern SignalStorage* g_signalStorage;

// 便捷访问宏
#define GET_SIGNAL(id) (g_signalStorage ? g_signalStorage->getSignal(id) : SignalData())
#define ADD_SIGNAL(signal) (g_signalStorage ? g_signalStorage->addSignal(signal) : false)
#define UPDATE_SIGNAL(id, signal) (g_signalStorage ? g_signalStorage->updateSignal(id, signal) : false)
#define DELETE_SIGNAL(id) (g_signalStorage ? g_signalStorage->deleteSignal(id) : false)
#define GET_ALL_SIGNALS() (g_signalStorage ? g_signalStorage->getAllSignals() : std::vector<SignalData>())

#endif

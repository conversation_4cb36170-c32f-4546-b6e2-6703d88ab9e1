/**
 * @file EventManager.h
 * @brief 事件管理器 - 双核架构事件系统
 * 
 * 基于前端完整数据文档的事件需求设计
 * 支持双核间事件通信和WebSocket事件广播
 * 
 * 作者：ESP32-S3红外控制系统
 * 版本：2.0
 * 日期：2025-01-01
 */

#ifndef EVENT_MANAGER_H
#define EVENT_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <functional>
#include <unordered_map>
#include <vector>
#include <queue>
#include <string>
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>
#include "../types/EventTypes.h"

// 事件处理器类型
using EventHandler = std::function<void(const EventData&)>;

// 事件订阅信息
struct EventSubscription {
    EventHandler handler;
    EventPriority minPriority;
    bool oneTime;               // 是否只执行一次
    uint32_t subscriptionId;    // 订阅ID
    
    EventSubscription(EventHandler h, EventPriority p = EventPriority::NORMAL, bool once = false)
        : handler(h), minPriority(p), oneTime(once), subscriptionId(0) {}
};

class EventManager {
private:
    // 事件订阅管理
    std::unordered_map<EventType, std::vector<EventSubscription>> eventHandlers;
    std::queue<EventData> eventQueue;
    
    // 线程安全
    SemaphoreHandle_t eventMutex;
    SemaphoreHandle_t queueMutex;
    
    // 状态管理
    bool initialized;
    bool processingEvents;
    uint32_t nextSubscriptionId;
    
    // 性能统计
    uint32_t totalEventsProcessed;
    uint32_t totalEventsQueued;
    uint32_t maxQueueSize;
    uint32_t lastProcessTime;

public:
    EventManager();
    ~EventManager();

    // 生命周期
    bool init();
    void cleanup();
    void loop();

    // 事件订阅管理
    uint32_t on(EventType type, EventHandler handler, 
                EventPriority minPriority = EventPriority::NORMAL);
    uint32_t once(EventType type, EventHandler handler,
                  EventPriority minPriority = EventPriority::NORMAL);
    bool off(EventType type, uint32_t subscriptionId = 0);
    void offAll(EventType type);
    void offAll();

    // 事件发布
    void emit(EventType type, const JsonDocument& data = JsonDocument());
    void emit(const EventData& event);
    void emitAsync(EventType type, const JsonDocument& data = JsonDocument());
    void emitAsync(const EventData& event);
    void emitPriority(EventType type, EventPriority priority, 
                     const JsonDocument& data = JsonDocument());

    // 批量事件处理
    void emitBatch(const std::vector<EventData>& events);
    void processBatch(const std::vector<EventData>& events);

    // 状态查询
    bool isInitialized() const { return initialized; }
    bool isProcessing() const { return processingEvents; }
    size_t getQueueSize() const { return eventQueue.size(); }
    size_t getMaxQueueSize() const { return maxQueueSize; }
    uint32_t getTotalEventsProcessed() const { return totalEventsProcessed; }
    uint32_t getTotalEventsQueued() const { return totalEventsQueued; }

    // 性能监控
    JsonDocument getStatistics() const;
    void resetStatistics();

    // 事件过滤和路由
    void setEventFilter(std::function<bool(const EventData&)> filter);
    void setEventRouter(std::function<void(const EventData&)> router);

private:
    // 事件处理核心
    void processEventQueue();
    void processEvent(const EventData& event);
    void executeHandlers(const EventData& event);
    
    // 订阅管理
    uint32_t addSubscription(EventType type, const EventSubscription& subscription);
    void removeSubscription(EventType type, uint32_t subscriptionId);
    void cleanupOneTimeSubscriptions(EventType type);
    
    // 线程安全辅助
    bool lockEventMutex(uint32_t timeoutMs = 1000);
    void unlockEventMutex();
    bool lockQueueMutex(uint32_t timeoutMs = 1000);
    void unlockQueueMutex();
    
    // 性能优化
    void updateStatistics();
    void optimizeQueue();
    bool shouldProcessEvent(const EventData& event);
    
    // 错误处理
    void handleEventError(const EventData& event, const std::string& error);
    void logEventProcessing(const EventData& event, bool success);

    // 事件过滤和路由
    std::function<bool(const EventData&)> eventFilter;
    std::function<void(const EventData&)> eventRouter;
};

// 全局事件管理器访问
extern EventManager* g_eventManager;

// 便捷宏定义
#define EMIT_EVENT(type, data) if(g_eventManager) g_eventManager->emit(type, data)
#define EMIT_EVENT_ASYNC(type, data) if(g_eventManager) g_eventManager->emitAsync(type, data)
#define EMIT_EVENT_PRIORITY(type, priority, data) if(g_eventManager) g_eventManager->emitPriority(type, priority, data)

#define SUBSCRIBE_EVENT(type, handler) if(g_eventManager) g_eventManager->on(type, handler)
#define SUBSCRIBE_EVENT_ONCE(type, handler) if(g_eventManager) g_eventManager->once(type, handler)
#define UNSUBSCRIBE_EVENT(type, id) if(g_eventManager) g_eventManager->off(type, id)

// 常用事件创建辅助函数
namespace EventUtils {
    EventData createSystemEvent(EventType type, const std::string& message = "");
    EventData createNetworkEvent(EventType type, const std::string& status = "");
    EventData createSignalEvent(EventType type, const std::string& signalId = "", 
                               const JsonDocument& signalData = JsonDocument());
    EventData createErrorEvent(const std::string& error, const std::string& source = "");
    EventData createStatusEvent(const JsonDocument& statusData);
    
    // WebSocket事件创建 (匹配前端需求)
    EventData createConnectedEvent(uint32_t clientId, const std::string& clientInfo = "");
    EventData createDisconnectedEvent(uint32_t clientId, const std::string& reason = "");
    EventData createSignalLearnedEvent(const std::string& signalId, 
                                      const JsonDocument& signalData,
                                      uint8_t quality = 100);
    EventData createSignalSentEvent(const std::string& signalId, 
                                   bool success = true,
                                   uint32_t duration = 0);
}

#endif
